# Bug 修复代码 - 多 Signer 问题

## 1. 修复方案概述

基于对代码的分析，问题的核心在于：
1. `ProcessSwapData` 方法中所有 SwapData 共享同一个 SwapInfo 实例
2. Signers 数组只包含一个元素（通常是第一个账户）
3. 缺少为每个指令单独提取对应 signer 的逻辑

## 2. 修复代码

### 2.1 修复 parser.go 中的 ProcessSwapData 方法

```go
// 文件: model-processor/txparser/parser.go
// 修复 ProcessSwapData 方法

func (p *Parser) ProcessSwapData(swapDatas []SwapData) ([]*SwapInfo, error) {
    swapInfos := make([]*SwapInfo, 0, len(swapDatas))
    if len(swapDatas) == 0 {
        return nil, fmt.Errorf("no swap data provided")
    }

    var sigs []solana.Signature
    for _, sigBytes := range p.txInfo.Signatures {
        sig := solana.SignatureFromBytes(sigBytes)
        sigs = append(sigs, sig)
    }

    // 修复：提取所有可能的 signers
    var allSigners []solana.PublicKey
    if p.containsDCAProgram() {
        allSigners = []solana.PublicKey{p.allAccountKeys[2]}
    } else {
        // 提取所有签名对应的账户
        signerCount := len(p.txInfo.Signatures)
        for i := 0; i < signerCount && i < len(p.allAccountKeys); i++ {
            allSigners = append(allSigners, p.allAccountKeys[i])
        }
        
        // 如果没有签名，至少包含第一个账户
        if len(allSigners) == 0 && len(p.allAccountKeys) > 0 {
            allSigners = []solana.PublicKey{p.allAccountKeys[0]}
        }
    }

    // 按协议类型分组处理
    jupiterSwaps := make([]SwapData, 0)
    pumpfunSwaps := make([]SwapData, 0)
    pumpswapSwaps := make([]SwapData, 0)
    pumpswapCreateSwaps := make([]SwapData, 0)
    otherSwaps := make([]SwapData, 0)

    for _, swapData := range swapDatas {
        switch swapData.Type {
        case JUPITER:
            jupiterSwaps = append(jupiterSwaps, swapData)
        case PUMP_FUN:
            pumpfunSwaps = append(pumpfunSwaps, swapData)
        case PUMP_SWAP:
            pumpswapSwaps = append(pumpswapSwaps, swapData)
        case PUMP_SWAP_CREATE:
            pumpswapCreateSwaps = append(pumpswapCreateSwaps, swapData)
        default:
            otherSwaps = append(otherSwaps, swapData)
        }
    }

    // 处理 Jupiter 交易
    if len(jupiterSwaps) > 0 {
        for _, swapData := range jupiterSwaps {
            swapInfo := &SwapInfo{
                Signatures:       sigs,
                Signers:         allSigners,
                InstructionIndex: swapData.InstructionIndex,
            }

            jupiterInfo, err := parseJupiterEvents([]SwapData{swapData})
            if err != nil {
                p.Log.Errorf("failed to parse Jupiter events: %v", err)
                continue
            }

            swapInfo.TokenInMint = jupiterInfo.TokenInMint
            swapInfo.TokenInAmount = jupiterInfo.TokenInAmount
            swapInfo.TokenInDecimals = jupiterInfo.TokenInDecimals
            swapInfo.TokenOutMint = jupiterInfo.TokenOutMint
            swapInfo.TokenOutAmount = jupiterInfo.TokenOutAmount
            swapInfo.TokenOutDecimals = jupiterInfo.TokenOutDecimals
            swapInfo.AMMs = jupiterInfo.AMMs
            
            // 修复：为每个交易设置正确的用户地址
            swapInfo.User = p.extractSignerForInstruction(swapData.InstructionIndex)
            
            swapInfos = append(swapInfos, swapInfo)
        }
    }

    // 处理 Pump.fun 交易
    if len(pumpfunSwaps) > 0 {
        for _, swapData := range pumpfunSwaps {
            swapInfo := &SwapInfo{
                Signatures:       sigs,
                Signers:         allSigners,
                InstructionIndex: swapData.InstructionIndex,
            }

            event := swapData.Data.(*PumpfunTradeEvent)
            if event.IsBuy {
                swapInfo.TokenInMint = NATIVE_SOL_MINT_PROGRAM_ID
                swapInfo.TokenInAmount = event.SolAmount
                swapInfo.TokenInDecimals = 9
                swapInfo.TokenOutMint = event.Mint
                swapInfo.TokenOutAmount = event.TokenAmount
                swapInfo.TokenOutDecimals = p.splDecimalsMap[event.Mint.String()]
                swapInfo.TxType = swapData.TxType
            } else {
                swapInfo.TokenInMint = event.Mint
                swapInfo.TokenInAmount = event.TokenAmount
                swapInfo.TokenInDecimals = p.splDecimalsMap[event.Mint.String()]
                swapInfo.TokenOutMint = NATIVE_SOL_MINT_PROGRAM_ID
                swapInfo.TokenOutAmount = event.SolAmount
                swapInfo.TokenOutDecimals = 9
                swapInfo.TxType = swapData.TxType
            }
            
            swapInfo.AMMs = append(swapInfo.AMMs, string(PUMP_FUN))
            swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
            
            // 修复：使用事件中的用户地址
            swapInfo.User = event.User
            
            swapInfos = append(swapInfos, swapInfo)
        }
    }

    // 处理 PumpSwap 交易
    if len(pumpswapSwaps) > 0 {
        for _, swapData := range pumpswapSwaps {
            swapInfo := &SwapInfo{
                Signatures:       sigs,
                Signers:         allSigners,
                InstructionIndex: swapData.InstructionIndex,
            }

            if swapData.TxType == "BUY" {
                event, ok := swapData.Data.(*PumpSwapBuyEvent)
                if !ok {
                    continue
                }
                swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
                swapInfo.TokenInAmount = event.MaxQuoteAmountIn
                swapInfo.TokenInMint = event.UserBaseTokenAccount
                swapInfo.TokenOutAmount = event.BaseAmountOut
                swapInfo.TokenOutMint = event.UserQuoteTokenAccount
                swapInfo.User = event.User
                swapInfo.TxType = swapData.TxType
            } else {
                event, ok := swapData.Data.(*PumpSwapSellEvent)
                if !ok {
                    continue
                }
                swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
                swapInfo.TokenInAmount = event.BaseAmountIn
                swapInfo.TokenInMint = event.UserBaseTokenAccount
                swapInfo.TokenOutAmount = event.UserQuoteAmountOut
                swapInfo.TokenOutMint = event.UserQuoteTokenAccount
                swapInfo.User = event.User
                swapInfo.TxType = swapData.TxType
            }
            
            swapInfos = append(swapInfos, swapInfo)
        }
    }

    // 处理 PumpSwap 创建交易
    if len(pumpswapCreateSwaps) > 0 {
        for _, swapData := range pumpswapCreateSwaps {
            swapInfo := &SwapInfo{
                Signatures:       sigs,
                Signers:         allSigners,
                InstructionIndex: swapData.InstructionIndex,
            }

            event, ok := swapData.Data.(*PumpSwapCreateEvent)
            if !ok {
                p.Log.Errorf("expected *PumpSwapCreateEvent but got %T", swapData.Data)
                continue
            }

            swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
            swapInfo.TokenInAmount = event.PoolBaseAmount
            swapInfo.TokenInMint = event.BaseMint
            swapInfo.TokenOutAmount = event.PoolQuoteAmount
            swapInfo.TokenOutMint = event.QuoteMint
            swapInfo.TxType = "ADD"
            swapInfo.User = event.Creator
            
            swapInfos = append(swapInfos, swapInfo)
        }
    }

    // 处理其他类型的交易
    if len(otherSwaps) > 0 {
        // 按指令索引分组
        instructionGroups := make(map[int][]SwapData)
        for _, swapData := range otherSwaps {
            instructionGroups[swapData.InstructionIndex] = append(instructionGroups[swapData.InstructionIndex], swapData)
        }

        for instructionIndex, swapGroup := range instructionGroups {
            swapInfo := &SwapInfo{
                Signatures:       sigs,
                Signers:         allSigners,
                InstructionIndex: instructionIndex,
            }

            var uniqueTokens []TokenTransfer
            seenTokens := make(map[string]bool)

            for _, swapData := range swapGroup {
                transfer := getTransferFromSwapData(swapData)
                if transfer != nil && !seenTokens[transfer.mint] {
                    uniqueTokens = append(uniqueTokens, *transfer)
                    seenTokens[transfer.mint] = true
                }
            }

            if len(uniqueTokens) >= 2 {
                inputTransfer := uniqueTokens[0]
                outputTransfer := uniqueTokens[len(uniqueTokens)-1]

                seenInputs := make(map[string]bool)
                seenOutputs := make(map[string]bool)
                var totalInputAmount uint64 = 0
                var totalOutputAmount uint64 = 0

                for _, swapData := range swapGroup {
                    transfer := getTransferFromSwapData(swapData)
                    if transfer == nil {
                        continue
                    }

                    amountStr := fmt.Sprintf("%d-%s", transfer.amount, transfer.mint)
                    if transfer.mint == inputTransfer.mint && !seenInputs[amountStr] {
                        totalInputAmount += transfer.amount
                        seenInputs[amountStr] = true
                    }
                    if transfer.mint == outputTransfer.mint && !seenOutputs[amountStr] {
                        totalOutputAmount += transfer.amount
                        seenOutputs[amountStr] = true
                    }
                }

                swapInfo.TokenInMint = solana.MustPublicKeyFromBase58(inputTransfer.mint)
                swapInfo.TokenInAmount = totalInputAmount
                swapInfo.TokenInDecimals = inputTransfer.decimals
                swapInfo.TokenOutMint = solana.MustPublicKeyFromBase58(outputTransfer.mint)
                swapInfo.TokenOutAmount = totalOutputAmount
                swapInfo.TokenOutDecimals = outputTransfer.decimals

                seenAMMs := make(map[string]bool)
                for _, swapData := range swapGroup {
                    if !seenAMMs[string(swapData.Type)] {
                        swapInfo.AMMs = append(swapInfo.AMMs, string(swapData.Type))
                        seenAMMs[string(swapData.Type)] = true
                    }
                }

                swapInfo.Timestamp = time.Now()
                
                // 修复：为每个指令设置正确的用户地址
                swapInfo.User = p.extractSignerForInstruction(instructionIndex)
                
                swapInfos = append(swapInfos, swapInfo)
            }
        }
    }

    return swapInfos, nil
}

// 新增：为指令提取对应的 signer
func (p *Parser) extractSignerForInstruction(instructionIndex int) solana.PublicKey {
    // 方法1: 基于指令中的账户信息推断
    if instructionIndex >= 0 && instructionIndex < len(p.txInfo.Message.Instructions) {
        instruction := p.txInfo.Message.Instructions[instructionIndex]
        
        // 通常第一个账户是操作的发起者
        if len(instruction.Accounts) > 0 {
            accountIndex := instruction.Accounts[0]
            if int(accountIndex) < len(p.allAccountKeys) {
                return p.allAccountKeys[accountIndex]
            }
        }
    }
    
    // 方法2: 基于签名数量推断
    signerCount := len(p.txInfo.Signatures)
    if instructionIndex >= 0 && instructionIndex < signerCount && instructionIndex < len(p.allAccountKeys) {
        return p.allAccountKeys[instructionIndex]
    }
    
    // 回退到第一个账户
    if len(p.allAccountKeys) > 0 {
        return p.allAccountKeys[0]
    }
    
    return solana.PublicKey{}
}
```

### 2.2 修复 transaction_service.go 中的 transformSwapInfo 方法

```go
// 文件: model-processor/service/transaction_service.go
// 修复 transformSwapInfo 方法

func transformSwapInfo(swapInfo *txparser.SwapInfo, txHash string, blockSlot uint64, txIndex int) *FinalTx {
    blockTime := time.Now().Unix() // Default
    if !swapInfo.Timestamp.IsZero() {
        blockTime = swapInfo.Timestamp.Unix()
    }

    userAddr := ""
    
    // 修复：优先使用协议特定的 User 字段
    if swapInfo.User != (solana.PublicKey{}) {
        userAddr = swapInfo.User.String()
    } else if len(swapInfo.Signers) > 0 {
        // 修复：改进 signer 选择逻辑
        signerIndex := 0
        
        // 如果有有效的指令索引，尝试使用对应的 signer
        if swapInfo.InstructionIndex >= 0 && swapInfo.InstructionIndex < len(swapInfo.Signers) {
            signerIndex = swapInfo.InstructionIndex
        }
        
        userAddr = swapInfo.Signers[signerIndex].String()
    }

    // 其余代码保持不变
    txType := "SWAP"
    mintAddr := ""
    if swapInfo.TxType == "" {
        if swapInfo.TokenInMint.String() == WSOL_ADDRESS && swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
            txType = "WSOL_INTERNAL"
            mintAddr = "WSOL_INTERNAL"
        } else if swapInfo.TokenInMint.String() == WSOL_ADDRESS {
            txType = "BUY"
            mintAddr = swapInfo.TokenOutMint.String()
        } else if swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
            txType = "SELL"
            mintAddr = swapInfo.TokenInMint.String()
        } else {
            txType = "SWAP"
            mintAddr = swapInfo.TokenOutMint.String()
        }
    } else {
        txType = swapInfo.TxType
        if swapInfo.TokenInMint.String() == WSOL_ADDRESS {
            mintAddr = swapInfo.TokenOutMint.String()
        } else if swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
            mintAddr = swapInfo.TokenInMint.String()
        } else {
            mintAddr = swapInfo.TokenOutMint.String()
        }
    }

    return &FinalTx{
        Ts:               blockTime,
        TxHash:           txHash,
        UserAddr:         userAddr,
        TokenInAddr:      swapInfo.TokenInMint.String(),
        TokenOutAddr:     swapInfo.TokenOutMint.String(),
        AmountIn:         swapInfo.TokenInAmount,
        AmountOut:        swapInfo.TokenOutAmount,
        TxType:           txType,
        BlockSlot:        blockSlot,
        InstructionIndex: txIndex,
        Network:          "solana",
        MintAddr:         mintAddr,
    }
}
```

## 3. 测试代码

### 3.1 单元测试

```go
// 文件: model-processor/txparser/parser_test.go

package txparser

import (
    "testing"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
    "github.com/gagliardetto/solana-go"
    "model-processor/proto"
)

func TestMultiSignerTransaction(t *testing.T) {
    // 创建测试用的多签名交易
    signer1 := solana.NewWallet().PublicKey()
    signer2 := solana.NewWallet().PublicKey()
    
    tx := &proto.Transaction{
        Signatures: [][]byte{
            make([]byte, 64), // signer1 的签名
            make([]byte, 64), // signer2 的签名
        },
        Message: &proto.TransactionMessage{
            AccountKeys: [][]byte{
                signer1.Bytes(),
                signer2.Bytes(),
                // 其他账户...
            },
            Instructions: []proto.CompiledInstruction{
                {
                    ProgramIdIndex: 2,
                    Accounts:       []uint32{0, 3, 4}, // 第一个指令使用 signer1
                },
                {
                    ProgramIdIndex: 2,
                    Accounts:       []uint32{1, 5, 6}, // 第二个指令使用 signer2
                },
            },
        },
    }

    txMeta := &proto.TransactionStatusMeta{
        // 模拟交易元数据
    }

    parser, err := NewTransactionParserFromTransaction(tx, txMeta, 0)
    require.NoError(t, err)

    // 创建测试用的 SwapData
    swapDatas := []SwapData{
        {
            Type:             RAYDIUM,
            InstructionIndex: 0,
            Data:             &TransferData{}, // 模拟数据
        },
        {
            Type:             RAYDIUM,
            InstructionIndex: 1,
            Data:             &TransferData{}, // 模拟数据
        },
    }

    swapInfos, err := parser.ProcessSwapData(swapDatas)
    require.NoError(t, err)
    require.Len(t, swapInfos, 2)

    // 验证每个 SwapInfo 使用了正确的 signer
    assert.Equal(t, signer1.String(), swapInfos[0].User.String())
    assert.Equal(t, signer2.String(), swapInfos[1].User.String())
}

func TestPumpfunMultiSigner(t *testing.T) {
    // 测试 Pump.fun 协议的多签名处理
    user1 := solana.NewWallet().PublicKey()
    user2 := solana.NewWallet().PublicKey()
    
    // 创建 Pump.fun 事件数据
    event1 := &PumpfunTradeEvent{
        User:   user1,
        IsBuy:  true,
        // 其他字段...
    }
    
    event2 := &PumpfunTradeEvent{
        User:   user2,
        IsBuy:  false,
        // 其他字段...
    }

    swapDatas := []SwapData{
        {
            Type:             PUMP_FUN,
            InstructionIndex: 0,
            Data:             event1,
        },
        {
            Type:             PUMP_FUN,
            InstructionIndex: 1,
            Data:             event2,
        },
    }

    // 创建解析器并测试
    parser := createTestParser(t)
    swapInfos, err := parser.ProcessSwapData(swapDatas)
    require.NoError(t, err)
    require.Len(t, swapInfos, 2)

    // 验证用户地址正确
    assert.Equal(t, user1.String(), swapInfos[0].User.String())
    assert.Equal(t, user2.String(), swapInfos[1].User.String())
}
```

## 4. 部署建议

### 4.1 部署步骤
1. **备份当前代码**: 确保可以回滚
2. **测试环境验证**: 在测试环境充分验证修复效果
3. **灰度发布**: 先在部分实例上部署
4. **监控数据质量**: 监控修复后的 UserAddr 分布
5. **全量部署**: 确认无问题后全量部署

### 4.2 监控指标
- UserAddr 的唯一值数量变化
- 不同协议的用户地址分布
- 数据库写入成功率
- 处理延迟变化

### 4.3 回滚计划
如果发现问题，可以快速回滚到原始代码，并分析具体的失败案例。
