# Model Processor Bug 分析报告 - 多 Signer 问题

## 1. 问题描述

### 1.1 Bug 现象
当一个 Transaction 中聚合了多个交易（存在多个 Signer）时，最终存储到 InfluxDB 的多条 MemeTx 数据中，所有记录的 `UserAddr` 字段都被设置为第一个 signer 的值，而不是各自对应的实际 signer。

### 1.2 影响范围
- **数据准确性**: 用户地址信息不准确，影响用户行为分析
- **业务逻辑**: 交易归属错误，影响用户交易统计
- **下游系统**: 依赖 UserAddr 的所有分析和统计功能都会受到影响

## 2. 问题根因分析

### 2.1 代码流程分析

#### 2.1.1 问题发生的调用链
```
TransactionService.processTransaction()
  ↓
Parser.ProcessSwapData()
  ↓
transformSwapInfo()
  ↓
mapFinalTxToMemeTx()
  ↓
Database.InsertMemeTx()
```

#### 2.1.2 关键问题代码

**问题代码位置**: `model-processor/txparser/parser.go` 第 208-212 行

```go
func (p *Parser) ProcessSwapData(swapDatas []SwapData) ([]*SwapInfo, error) {
    // ... 其他代码 ...
    
    swapInfo := &SwapInfo{
        Signatures: sigs,
    }

    if p.containsDCAProgram() {
        swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[2]}  // 问题：只设置一个 signer
    } else {
        swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[0]}  // 问题：只设置一个 signer
    }
    
    // ... 后续处理 ...
}
```

**问题代码位置**: `model-processor/service/transaction_service.go` 第 119-128 行

```go
func transformSwapInfo(swapInfo *txparser.SwapInfo, txHash string, blockSlot uint64, txIndex int) *FinalTx {
    // ... 其他代码 ...
    
    userAddr := ""
    if swapInfo.User != (solana.PublicKey{}) {
        userAddr = swapInfo.User.String()
    } else if len(swapInfo.Signers) > 0 {
        if len(swapInfo.Signers) > swapInfo.InstructionIndex {
            userAddr = swapInfo.Signers[swapInfo.InstructionIndex].String()  // 尝试使用指令索引
        } else {
            userAddr = swapInfo.Signers[0].String()  // 回退到第一个 signer
        }
    }
    
    // ... 其他代码 ...
}
```

### 2.2 问题分析

#### 2.2.1 根本原因
1. **Signers 数组初始化错误**: 在 `ProcessSwapData` 方法中，`swapInfo.Signers` 只被初始化为包含一个元素的数组，始终是 `allAccountKeys[0]` 或 `allAccountKeys[2]`

2. **缺少多 Signer 处理逻辑**: 代码没有正确处理包含多个 signer 的交易场景

3. **InstructionIndex 逻辑缺陷**: 虽然 `transformSwapInfo` 中有使用 `InstructionIndex` 的逻辑，但由于 `Signers` 数组只有一个元素，这个逻辑无法正常工作

#### 2.2.2 设计缺陷
- **单一 SwapInfo 复用**: 所有的 SwapData 都共享同一个 SwapInfo 实例，导致 signer 信息被覆盖
- **缺少指令级别的 signer 提取**: 没有为每个指令单独提取对应的 signer

## 3. 正确的 Solana 交易结构理解

### 3.1 Solana 交易结构
```go
type Transaction struct {
    Signatures [][]byte           // 多个签名
    Message    TransactionMessage // 交易消息
}

type TransactionMessage struct {
    AccountKeys [][]byte           // 账户密钥列表
    Instructions []CompiledInstruction // 指令列表
    // ...
}
```

### 3.2 多 Signer 场景
- **聚合交易**: Jupiter 等聚合器可能在一个交易中包含多个用户的操作
- **批量操作**: 一个交易中可能包含多个独立的交换操作
- **复合指令**: 不同指令可能由不同的账户签名

## 4. 解决方案

### 4.1 方案一：修复 Signers 数组初始化（推荐）

#### 4.1.1 修改 ProcessSwapData 方法
```go
func (p *Parser) ProcessSwapData(swapDatas []SwapData) ([]*SwapInfo, error) {
    swapInfos := make([]*SwapInfo, 0, len(swapDatas))
    if len(swapDatas) == 0 {
        return nil, fmt.Errorf("no swap data provided")
    }

    var sigs []solana.Signature
    for _, sigBytes := range p.txInfo.Signatures {
        sig := solana.SignatureFromBytes(sigBytes)
        sigs = append(sigs, sig)
    }

    // 修复：正确提取所有 signers
    var allSigners []solana.PublicKey
    if p.containsDCAProgram() {
        // DCA 程序的特殊处理
        allSigners = []solana.PublicKey{p.allAccountKeys[2]}
    } else {
        // 提取所有可能的 signers
        // 通常第一个账户是 fee payer，但可能有多个 signers
        signerCount := len(p.txInfo.Signatures)
        for i := 0; i < signerCount && i < len(p.allAccountKeys); i++ {
            allSigners = append(allSigners, p.allAccountKeys[i])
        }
    }

    // 为每个 SwapData 创建独立的 SwapInfo
    for _, swapData := range swapDatas {
        swapInfo := &SwapInfo{
            Signatures:       sigs,
            Signers:         allSigners,
            InstructionIndex: swapData.InstructionIndex,
        }

        // 根据协议类型处理
        switch swapData.Type {
        case JUPITER:
            // Jupiter 处理逻辑
            if err := p.processJupiterSwapInfo(swapInfo, swapData); err != nil {
                continue
            }
        case PUMP_FUN:
            // Pump.fun 处理逻辑
            if err := p.processPumpfunSwapInfo(swapInfo, swapData); err != nil {
                continue
            }
        // ... 其他协议处理
        }

        swapInfos = append(swapInfos, swapInfo)
    }

    return swapInfos, nil
}
```

#### 4.1.2 修改 transformSwapInfo 方法
```go
func transformSwapInfo(swapInfo *txparser.SwapInfo, txHash string, blockSlot uint64, txIndex int) *FinalTx {
    blockTime := time.Now().Unix()
    if !swapInfo.Timestamp.IsZero() {
        blockTime = swapInfo.Timestamp.Unix()
    }

    userAddr := ""
    
    // 优先使用协议特定的 User 字段
    if swapInfo.User != (solana.PublicKey{}) {
        userAddr = swapInfo.User.String()
    } else if len(swapInfo.Signers) > 0 {
        // 修复：正确使用指令索引获取对应的 signer
        signerIndex := 0
        if swapInfo.InstructionIndex >= 0 && swapInfo.InstructionIndex < len(swapInfo.Signers) {
            signerIndex = swapInfo.InstructionIndex
        }
        userAddr = swapInfo.Signers[signerIndex].String()
    }

    // ... 其余代码保持不变
}
```

### 4.2 方案二：基于指令提取 Signer（备选）

#### 4.2.1 添加指令级别的 Signer 提取
```go
func (p *Parser) extractSignerForInstruction(instructionIndex int) solana.PublicKey {
    // 根据指令类型和账户使用情况推断 signer
    if instructionIndex < len(p.txInfo.Message.Instructions) {
        instruction := p.txInfo.Message.Instructions[instructionIndex]
        
        // 通常第一个账户是 signer
        if len(instruction.Accounts) > 0 {
            accountIndex := instruction.Accounts[0]
            if int(accountIndex) < len(p.allAccountKeys) {
                return p.allAccountKeys[accountIndex]
            }
        }
    }
    
    // 回退到第一个账户
    if len(p.allAccountKeys) > 0 {
        return p.allAccountKeys[0]
    }
    
    return solana.PublicKey{}
}
```

### 4.3 方案三：协议特定的 Signer 提取（最准确）

#### 4.3.1 为每个协议实现特定的 Signer 提取逻辑
```go
func (p *Parser) extractSignerFromPumpfunEvent(event *PumpfunTradeEvent) solana.PublicKey {
    // Pump.fun 事件中直接包含用户地址
    return event.User
}

func (p *Parser) extractSignerFromJupiterEvent(event *JupiterRouteEvent) solana.PublicKey {
    // Jupiter 事件中包含用户地址
    return event.User
}

func (p *Parser) extractSignerFromTransferData(data *TransferData, instructionIndex int) solana.PublicKey {
    // 从转账数据中提取 authority 作为 signer
    if authority, err := solana.PublicKeyFromBase58(data.Info.Authority); err == nil {
        return authority
    }
    
    // 回退逻辑
    return p.extractSignerForInstruction(instructionIndex)
}
```

## 5. 推荐解决方案

### 5.1 实施步骤

1. **第一阶段**: 实施方案一，修复基础的 Signers 数组初始化问题
2. **第二阶段**: 为每个协议实现特定的 Signer 提取逻辑（方案三）
3. **第三阶段**: 添加测试用例验证修复效果

### 5.2 测试验证

#### 5.2.1 测试用例设计
```go
func TestMultiSignerTransaction(t *testing.T) {
    // 构造包含多个 signer 的测试交易
    tx := &proto.Transaction{
        Signatures: [][]byte{
            signature1, // 第一个 signer 的签名
            signature2, // 第二个 signer 的签名
        },
        Message: &proto.TransactionMessage{
            AccountKeys: [][]byte{
                signer1Key,  // 第一个 signer
                signer2Key,  // 第二个 signer
                // ... 其他账户
            },
            Instructions: []proto.CompiledInstruction{
                // 第一个指令，应该使用 signer1
                {ProgramIdIndex: 0, Accounts: []uint32{0, ...}},
                // 第二个指令，应该使用 signer2
                {ProgramIdIndex: 1, Accounts: []uint32{1, ...}},
            },
        },
    }

    parser, err := NewTransactionParser(tx)
    require.NoError(t, err)

    swapInfos, err := parser.ProcessSwapData(swapDatas)
    require.NoError(t, err)

    // 验证每个 SwapInfo 的 UserAddr 是否正确
    assert.Equal(t, signer1Key.String(), swapInfos[0].User.String())
    assert.Equal(t, signer2Key.String(), swapInfos[1].User.String())
}
```

### 5.3 风险评估

#### 5.3.1 低风险
- 修复逻辑相对简单
- 不影响现有的单 signer 交易处理
- 向后兼容

#### 5.3.2 需要注意的点
- 确保不同协议的 signer 提取逻辑正确
- 测试各种边界情况
- 监控修复后的数据质量

## 6. 总结

这个 bug 的根本原因是在处理多 signer 交易时，代码错误地将所有交易都归属给了第一个 signer。通过修复 `ProcessSwapData` 方法中的 Signers 数组初始化逻辑，并改进 `transformSwapInfo` 中的 signer 选择逻辑，可以有效解决这个问题。

建议采用分阶段的修复方案，先解决基础问题，再逐步完善各协议的特定处理逻辑，确保数据的准确性和系统的稳定性。
