# Chain Quote Server Makefile
# 提供构建、运行和管理项目的命令

# 声明所有目标为伪目标，避免与同名文件冲突
.PHONY: build run run-dev clean protoc debug help install-deps darwin linux linux-arm64 windows test build-transaction build-balance run-transaction run-balance run-dev-transaction run-dev-balance

# 默认目标
all: build

# 帮助信息
help:
	@echo "Chain Quote Server Makefile"
	@echo "可用命令:"
	@echo "  make build                - 构建所有服务"
	@echo "  make run                  - 运行原始项目"
	@echo "  make run-dev              - 以开发模式运行原始项目"
	@echo "  make clean                - 清理构建产物"
	@echo "  make darwin               - 构建 macOS 版本"
	@echo "  make linux                - 构建 Linux x86_64 版本"
	@echo "  make linux-arm64          - 构建 Linux ARM64 版本 (ARM 架构)"
	@echo "  make windows              - 构建 Windows 版本"
	@echo "  make protoc               - 生成 protobuf 代码"
	@echo "  make debug                - 构建带调试信息的版本"
	@echo "  make install-deps         - 安装依赖项"
	@echo "  make test                 - 运行测试"

# 变量定义
BINARY_NAME=grpc-client
BINARY_PATH=./bin/$(BINARY_NAME)
MAIN_PACKAGE=./cmd/grpc-client


BUILD_FLAGS=-ldflags="-s -w"
DEBUG_FLAGS=-gcflags="all=-N -l"


# 平台特定构建 - 更新为构建所有服务
darwin:
	GOOS=darwin GOARCH=amd64 go build -o $(BINARY_PATH)-darwin-amd64 $(BUILD_FLAGS) $(MAIN_PACKAGE)
	@echo "构建完成: Darwin 平台服务"

linux:
	GOOS=linux GOARCH=amd64 go build -o $(BINARY_PATH)-linux-amd64 $(BUILD_FLAGS) $(MAIN_PACKAGE)
	@echo "构建完成: Linux x86_64 平台服务"

# Linux ARM64 构建 (适用于 ARM 架构如 Raspberry Pi 或 AWS Graviton)
linux-arm64:
	GOOS=linux GOARCH=arm64 go build -o $(BINARY_PATH)-linux-arm64 $(BUILD_FLAGS) $(MAIN_PACKAGE)
	@echo "构建完成: Linux ARM64 平台服务"

windows:
	GOOS=windows GOARCH=amd64 go build -o $(BINARY_PATH)-windows-amd64.exe $(BUILD_FLAGS) $(MAIN_PACKAGE)
	@echo "构建完成: $(BINARY_PATH)-windows-amd64.exe"

# 运行项目
run: build
	$(BINARY_PATH)

# 开发模式运行
run-dev:
	go run $(MAIN_PACKAGE)

# 调试构建
debug:
	go build -o $(BINARY_PATH)-debug $(DEBUG_FLAGS) $(MAIN_PACKAGE)
	@echo "调试构建完成: $(BINARY_PATH)-debug"

# 清理
clean:
	@rm -rf bin/
	@echo "清理完成"

# 安装依赖项
install-deps:
	go mod tidy
	go mod download
	@echo "依赖项安装完成"

# 运行测试
test:
	go test -v ./...

# 生成 protobuf 代码
protoc:
	protoc \
		--go_out=./proto \
		--go_opt=paths=source_relative \
		--go-grpc_out=./proto \
		--go-grpc_opt=paths=source_relative \
		--proto_path ../../yellowstone-grpc-proto/proto/ ../../yellowstone-grpc-proto/proto/*.proto
	@echo "Protobuf 代码生成完成"
