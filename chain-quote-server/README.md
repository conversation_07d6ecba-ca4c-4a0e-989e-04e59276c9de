# Chain Quote Server

区块链交易和余额监控服务

## 项目结构

该项目已拆分为两个独立的服务：

1. **交易监听服务 (Transaction Service)**
   - 路径: `cmd/transaction-service/`
   - 功能: 通过gRPC订阅Solana网络交易，并将交易数据发送到Kafka
   - 使用的Kafka主题: `origin-tx`

2. **余额监听服务 (Balance Service)**
   - 路径: `cmd/balance-service/`
   - 功能: 监听地址更新，追踪用户余额变动
   - 消费的Kafka主题: `update-address-topic`


3. **两者都有 (Balance Service)**
   - 路径: `cmd/grpc-client/`
   - 功能: 监听地址更新，追踪用户余额变动,通过gRPC订阅Solana网络交易，并将交易数据发送到Kafka


## 配置

在 `config.toml` 文件中配置服务参数：

```toml
# gRPC 连接配置
[grpc]
endpoint = "http://example.com:10000"
token = "YOUR_TOKEN"
insecure = false

# 交易订阅配置
[transactions]
enabled = true
# ... 其他交易相关配置

# Kafka 配置
[kafka]
brokers = ["kafka:9092"]
topic = "origin-tx"  # 交易服务发送的主题
consumer_topic = "update-address-topic"  # 余额服务订阅的主题
active_address_topic = "active-address"
```

## 启动服务

### 交易监听服务

```bash
go run cmd/transaction-service/main.go
```

### 余额监听服务

```bash
go run cmd/balance-service/main.go
```

## 服务拆分优势

1. 独立扩展：可以根据需求分别扩展两个服务
2. 故障隔离：一个服务的问题不会影响另一个服务
3. 资源优化：可以根据实际负载为每个服务分配不同的资源

## 代码流程与逻辑

### 整体架构

系统采用微服务架构，使用Kafka作为服务间通信的消息队列，主要分为以下几个核心组件：

1. **gRPC客户端**：连接区块链节点，订阅区块链事件
2. **交易监听服务**：处理链上交易数据
3. **余额监听服务**：监控地址余额变化
4. **Kafka消息队列**：用于服务间通信和数据传输

### 详细流程

#### 1. 交易监听服务流程

1. **启动流程**：
   - 加载配置文件`config.toml`
   - 验证gRPC连接配置
   - 建立与区块链节点的gRPC连接
   - 使用优雅退出机制处理信号中断

2. **gRPC订阅流程**：
   - 构建区块链事件订阅请求（交易、账户更新等）
   - 根据配置文件和存储的监控地址列表动态生成订阅过滤条件
   - 启动统计信息打印goroutine监控数据流量

3. **数据处理流程**：
   - 接收gRPC流数据
   - 根据数据类型（交易、账户更新）分发到不同处理器
   - 对交易数据进行解析和处理
   - 记录数据统计信息（接收、发送计数）

4. **Kafka生产者**：
   - 将处理后的交易数据发送到`origin-tx`主题
   - 如果配置了活跃地址跟踪，将活跃地址信息发送到`active-address`主题

#### 2. 余额监听服务流程

1. **启动流程**：
   - 加载配置文件
   - 根据配置创建Kafka消费者组
   - 设置消息处理函数
   - 使用优雅退出机制处理信号中断

2. **Kafka消费流程**：
   - 订阅`update-address-topic`主题
   - 消费地址更新消息
   - 处理地址变更通知

3. **地址管理**：
   - 解析地址更新消息
   - 更新本地存储的监控地址列表
   - 触发gRPC重新订阅（当监控地址列表变更时）

4. **重订阅机制**：
   - 通过通道触发重新订阅事件
   - 中断当前gRPC订阅流并使用更新后的地址列表重新订阅

### 关键技术点

1. **异步处理**：
   - 使用goroutine处理并发任务
   - 使用WaitGroup和Context管理goroutine生命周期
   - 使用通道在goroutine间通信

2. **安全连接**：
   - 支持TLS加密连接到gRPC服务
   - 支持AWS MSK Serverless（可选），包括IAM认证

3. **容错机制**：
   - 实现指数退避重连策略
   - 使用Kafka确保消息不丢失
   - 优雅退出机制确保资源正确释放

4. **可扩展性**：
   - 模块化设计使服务可独立扩展
   - 配置驱动的功能开关
   - 多种部署选项（普通Kafka或AWS MSK）

### 数据流向

```
区块链节点 → gRPC → 交易监听服务 → Kafka(origin-tx) → 
[下游数据处理] → Kafka(update-address-topic) → 余额监听服务 → 
[更新监控地址] → 交易监听服务重新订阅
```

## 核心文件详解

### pkg/grpc/subscribe.go

这个文件是系统中连接区块链的核心组件，负责建立gRPC连接并处理区块链事件数据：

#### 主要功能

1. **gRPC连接建立**：
   - 支持安全（TLS）和非安全连接模式
   - 配置证书池和保活参数
   - 支持Token认证机制

2. **订阅请求构建**：
   - 动态构建订阅过滤条件
   - 支持多种区块链数据类型：
     * 区块槽位（Slots）
     * 区块数据（Blocks）
     * 区块元数据（BlocksMeta）
     * 账户数据（Accounts）
     * 交易数据（Transactions）
   - 根据存储的监控地址列表自动调整订阅范围

3. **数据接收与处理**：
   - 实现持久化的接收循环
   - 区分并处理不同类型的区块链事件
   - 支持断线重连和错误恢复
   - 维护统计计数器监控数据流量

4. **Kafka生产者集成**：
   - 将处理后的数据发送到对应Kafka主题
   - 支持AWS MSK Serverless和标准Kafka
   - 实现降级机制确保服务可靠性




subscribe 就是订阅交易，然后初始化kafka的生产者

然后kafka这里主要是支持的初始化生产者和消费者 
生产者主要是用来生产origin tx的 和活跃地址的余额变动，现在这部分其实并没去掉
消费者就是用来消费kafka 数据的，就是活跃地址的接收，后买呢models包括stroage 都是干这个事情，

这个服务比较简单,只是grpc的部分的话，就是初始化，grpc 订阅，kafka 异步写入


## TODO
- [x] connect grpc
- [ ] Add storage
- [ ] Add more methods about which things to download
- [ ] Add more tests
- [ ] Add more examples