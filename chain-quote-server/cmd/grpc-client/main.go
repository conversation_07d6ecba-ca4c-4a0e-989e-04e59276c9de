package main

import (
	"context"
	"log"
	"net/url"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"chain-quote-server/config"
	"chain-quote-server/pkg/grpc"
)

func main() {
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Println("启动 交易监听服务...")

	// 加载配置
	cfg := config.Load()

	// 验证GRPC配置
	if cfg.GRPC.Endpoint == "" {
		log.Fatalf("需要提供 GRPC 地址。请在 config.toml 文件中设置 grpc.endpoint。")
	}

	// 解析GRPC地址
	u, err := url.Parse(cfg.GRPC.Endpoint)
	if err != nil {
		log.Fatalf("提供的 GRPC 地址无效: %v", err)
	}

	if u.Scheme == "http" {
		cfg.GRPC.Insecure = true
	}

	// 获取端口和主机名
	port := u.Port()
	if port == "" {
		if cfg.GRPC.Insecure {
			port = "80"
		} else {
			port = "443"
		}
	}
	hostname := u.Hostname()
	if hostname == "" {
		log.Fatalf("请提供 URL 格式的端点，例如 http(s)://<endpoint>:<port>")
	}

	address := hostname + ":" + port

	// 建立gRPC连接
	conn, err := grpc.Connect(address, cfg.GRPC.Insecure)
	if err != nil {
		log.Fatalf("无法连接到gRPC服务器: %v", err)
	}
	defer conn.Close()

	// 设置用于跨goroutine取消的上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 用于管理goroutine的WaitGroup
	var wg sync.WaitGroup

	// 启动gRPC订阅goroutine（专注于交易监听）
	wg.Add(1)
	go func() {
		defer wg.Done()
		if err := grpc.StartSubscription(ctx, conn, cfg); err != nil {
			log.Printf("gRPC订阅服务发生错误: %v", err)
			// 通知主程序需要退出
			cancel()
		}
	}()

	// 处理信号以优雅地退出
	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	sig := <-sigterm
	log.Printf("收到信号 %v，正在优雅地关闭...", sig)

	// 创建一个带超时的上下文用于关闭
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer shutdownCancel()

	// 通知所有goroutine开始关闭
	cancel()

	// 创建一个通道用于等待关闭完成
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	// 等待关闭完成或超时
	select {
	case <-done:
		log.Println("所有goroutine已完成")
	case <-shutdownCtx.Done():
		log.Println("关闭超时，强制退出")
	}

	// 确保连接被关闭
	if conn != nil {
		log.Println("关闭gRPC连接...")
		conn.Close()
	}

	log.Println("交易监听服务已结束")
	os.Exit(0)
}
