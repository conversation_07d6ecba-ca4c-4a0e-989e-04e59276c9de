# gRPC 连接配置，这个是需要付费去买的
[grpc]
endpoint = "http://127.0.0.1:10000"
token = ""
insecure = false

# 区块链数据订阅配置
[blockchain]
slots = false
blocks = false
blocks_meta = false
signature = ""
resub = 0

# 账户订阅配置
[accounts]
enabled = false
filter = []
owner = []

# 交易订阅配置
[transactions]
enabled = true
vote = false
failed = false
account_include = [
    "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
    "DCAK36VfExkPdAkYUQg6ewgxyinvcEyPLyHjRbmveKFw",
    "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
    "PhoeNiXZ8ByJGLkxNfZRnkUfjvmuYqLR89jjFHGqdXY",
    "pAMMBay6oceH9fJKBRHGP5D4bD4sWpmSwMn52FMfXEA",
    "BANANAjs7FJiPQqJTGFzkZJndT9o7UmKiYYGaJz6frGu",
    "minTcHYRLVPubRK8nt6sqe2ZpWrGDLQoNLipDJCGocY",
    "b1oomGGqPKGD6errbyfbVMBuzSC8WtAAYo8MwNafWW1",
    "MaestroAAe9ge5HTc64VbBQZ6fP77pwvrhM8i1XWSAx",
    "NoVA1TmDUqksaj2hB1nayFkPysjJbFiU76dT4qPw2wm",
]
account_exclude = []

# Kafka 配置
[kafka]
brokers = ["127.0.0.1:9092"]
use_msk = false
msk_region = "eu-central-1"
msk_use_iam_auth = true
topic = "origin-tx"
consumer_topic = "update-address-topic"
active_address_topic = "active-address"

[worker_pool]
size = 10
