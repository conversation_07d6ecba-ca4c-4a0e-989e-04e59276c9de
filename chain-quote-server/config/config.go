package config

import (
	"log"
	"os"
	"path/filepath"

	"github.com/BurntSushi/toml"
)

// Config 存储所有配置信息
type Config struct {
	GRPC         GRPCConfig         `toml:"grpc"`
	Blockchain   BlockchainConfig   `toml:"blockchain"`
	Accounts     AccountsConfig     `toml:"accounts"`
	Transactions TransactionsConfig `toml:"transactions"`
	Kafka        KafkaConfig        `toml:"kafka"`
	WorkerPool   WorkerPoolConfig   `toml:"worker_pool"`
}

// GRPCConfig gRPC 连接配置
type GRPCConfig struct {
	Endpoint string `toml:"endpoint"`
	Token    string `toml:"token"`
	Insecure bool   `toml:"insecure"`
}

// BlockchainConfig 区块链数据订阅配置
type BlockchainConfig struct {
	Slots      bool   `toml:"slots"`
	Blocks     bool   `toml:"blocks"`
	BlocksMeta bool   `toml:"blocks_meta"`
	Signature  string `toml:"signature"`
	Resub      uint   `toml:"resub"`
}

// AccountsConfig 账户订阅配置
type AccountsConfig struct {
	Enabled bool     `toml:"enabled"`
	Filter  []string `toml:"filter"`
	Owner   []string `toml:"owner"`
}

// TransactionsConfig 交易订阅配置
type TransactionsConfig struct {
	Enabled        bool     `toml:"enabled"`
	Vote           bool     `toml:"vote"`
	Failed         bool     `toml:"failed"`
	AccountInclude []string `toml:"account_include"`
	AccountExclude []string `toml:"account_exclude"`
}

// KafkaConfig Kafka 配置
type KafkaConfig struct {
	Brokers            []string `toml:"brokers"`
	Topic              string   `toml:"topic"`
	ConsumerTopic      string   `toml:"consumer_topic"`
	ActiveAddressTopic string   `toml:"active_address_topic"`

	// AWS MSK Serverless 配置
	UseMSK        bool   `toml:"use_msk"`          // 是否使用 AWS MSK Serverless
	MSKRegion     string `toml:"msk_region"`       // AWS 区域，例如 eu-central-1
	MSKUseIAMAuth bool   `toml:"msk_use_iam_auth"` // 是否使用 IAM 认证
}

// WorkerPoolConfig 工作池配置
type WorkerPoolConfig struct {
	Size int `toml:"size"` // 工作池大小
}

// Load 从 TOML 文件加载配置
func Load() *Config {
	config := &Config{}

	// 获取当前工作目录
	workDir, err := os.Getwd()
	if err != nil {
		log.Fatalf("获取工作目录失败: %v", err)
	}

	// 查找 config.toml 文件
	configPath := filepath.Join(workDir, "config.toml")
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		log.Fatalf("配置文件不存在: %s", configPath)
	}

	// 解析 TOML 文件
	if _, err := toml.DecodeFile(configPath, config); err != nil {
		log.Fatalf("解析配置文件失败: %v", err)
	}

	return config
}
