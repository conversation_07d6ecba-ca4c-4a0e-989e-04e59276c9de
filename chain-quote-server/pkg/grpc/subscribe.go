package grpc

import (
	"context"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"sync/atomic"
	"time"

	"chain-quote-server/config"
	"chain-quote-server/pkg/kafka"
	"chain-quote-server/pkg/models"
	pb "chain-quote-server/proto"

	"github.com/gagliardetto/solana-go"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/proto"
)

// 交易统计
var (
	receivedTxCount     uint64 // 接收到的交易数量
	sentTxCount         uint64 // 成功发送的交易数量
	receivedStatusCount uint64 // 接收到的状态更新数量
	sentStatusCount     uint64 // 成功发送的状态更新数量
)

// Connect 创建并返回gRPC连接
func Connect(address string, plaintext bool) (*grpc.ClientConn, error) {
	var opts []grpc.DialOption

	// 配置 TLS
	if plaintext {
		opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
	} else {
		pool, _ := x509.SystemCertPool()
		creds := credentials.NewClientTLSFromCert(pool, "")
		opts = append(opts, grpc.WithTransportCredentials(creds))
	}

	// 添加保活参数
	opts = append(opts, grpc.WithKeepaliveParams(models.GRPCKeepaliveParams))

	log.Println("启动 gRPC 客户端，连接到", address)
	conn, err := grpc.Dial(address, opts...)
	if err != nil {
		return nil, err
	}

	return conn, nil
}

// buildSubscriptionRequest 根据配置和当前监控的地址构建订阅请求
func buildSubscriptionRequest(cfg *config.Config) pb.SubscribeRequest {
	commitment := pb.CommitmentLevel_PROCESSED
	var subscription = pb.SubscribeRequest{
		Commitment: &commitment,
	}

	// Configure slots subscription
	if cfg.Blockchain.Slots {
		if subscription.Slots == nil {
			subscription.Slots = make(map[string]*pb.SubscribeRequestFilterSlots)
		}
		subscription.Slots["slots"] = &pb.SubscribeRequestFilterSlots{}
	}

	// Configure block subscription
	if cfg.Blockchain.Blocks {
		if subscription.Blocks == nil {
			subscription.Blocks = make(map[string]*pb.SubscribeRequestFilterBlocks)
		}
		subscription.Blocks["blocks"] = &pb.SubscribeRequestFilterBlocks{}
	}

	// Configure block metadata subscription
	if cfg.Blockchain.BlocksMeta {
		if subscription.BlocksMeta == nil {
			subscription.BlocksMeta = make(map[string]*pb.SubscribeRequestFilterBlocksMeta)
		}
		subscription.BlocksMeta["block_meta"] = &pb.SubscribeRequestFilterBlocksMeta{}
	}

	// --- Configure Transactions Subscription ---
	if subscription.Transactions == nil {
		subscription.Transactions = make(map[string]*pb.SubscribeRequestFilterTransactions)
	}

	// Configure specific signature transaction subscription
	if cfg.Blockchain.Signature != "" {
		tr := true
		subscription.Transactions["signature_sub"] = &pb.SubscribeRequestFilterTransactions{
			AccountInclude: cfg.Accounts.Filter,
			Failed:         &tr,
			Vote:           &tr,
			Signature:      &cfg.Blockchain.Signature,
		}
	}

	// Configure general transaction subscription
	if cfg.Transactions.Enabled {
		subscription.Transactions["transactions_sub"] = &pb.SubscribeRequestFilterTransactions{
			Failed:         &cfg.Transactions.Failed,
			Vote:           &cfg.Transactions.Vote,
			AccountInclude: cfg.Transactions.AccountInclude,
			AccountExclude: cfg.Transactions.AccountExclude,
		}
	}
	// --- End Transactions Subscription ---

	return subscription
}

// StartSubscription 启动gRPC订阅服务
func StartSubscription(ctx context.Context, conn *grpc.ClientConn, cfg *config.Config) error {
	log.Println("gRPC subscription service started")
	defer log.Println("gRPC subscription service finished")

	// 启动统计信息打印goroutine
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		previousReceived := uint64(0)
		previousSent := uint64(0)
		previousReceivedStatus := uint64(0)
		previousSentStatus := uint64(0)
		startTime := time.Now()

		for {
			select {
			case <-ticker.C:
				currentTime := time.Now()
				elapsed := currentTime.Sub(startTime).Seconds()

				received := atomic.LoadUint64(&receivedTxCount)
				sent := atomic.LoadUint64(&sentTxCount)
				receivedStatus := atomic.LoadUint64(&receivedStatusCount)
				sentStatus := atomic.LoadUint64(&sentStatusCount)

				// 计算增量和速率
				receivedDiff := received - previousReceived
				sentDiff := sent - previousSent
				receivedStatusDiff := receivedStatus - previousReceivedStatus
				sentStatusDiff := sentStatus - previousSentStatus

				receivedRate := float64(receivedDiff) / 30.0
				sentRate := float64(sentDiff) / 30.0
				receivedStatusRate := float64(receivedStatusDiff) / 30.0
				sentStatusRate := float64(sentStatusDiff) / 30.0

				// 合并两个日志为一个全面的统计信息
				log.Printf("交易统计 | 30秒: 接收=%d (%.1f/s), 发送=%d (%.1f/s), 状态接收=%d (%.1f/s), 状态发送=%d (%.1f/s) | 总计(%.1f秒): 接收=%d (%.1f/s), 发送=%d (%.1f/s), 状态接收=%d (%.1f/s), 状态发送=%d (%.1f/s)",
					receivedDiff, receivedRate, sentDiff, sentRate, receivedStatusDiff, receivedStatusRate, sentStatusDiff, sentStatusRate,
					elapsed, received, float64(received)/elapsed, sent, float64(sent)/elapsed, receivedStatus, float64(receivedStatus)/elapsed, sentStatus, float64(sentStatus)/elapsed)

				// 更新前一次的计数
				previousReceived = received
				previousSent = sent
				previousReceivedStatus = receivedStatus
				previousSentStatus = sentStatus

			case <-ctx.Done():
				return
			}
		}
	}()

	client := pb.NewGeyserClient(conn)

	// 初始化交易的Kafka生产者（如果配置了）
	var txProducer *kafka.AsyncTransactionProducer
	if cfg.Kafka.Topic != "" {
		var err error
		if cfg.Kafka.UseMSK && cfg.Kafka.MSKUseIAMAuth && cfg.Kafka.MSKRegion != "" {
			log.Printf("使用AWS MSK Serverless (IAM认证, 区域: %s) 作为交易生产者", cfg.Kafka.MSKRegion)
			txProducer, err = kafka.NewMSKServerlessProducer(cfg.Kafka.Brokers, cfg.Kafka.Topic, cfg.Kafka.MSKRegion)
			if err != nil {
				log.Printf("ERROR: Failed to create MSK Serverless producer for transactions: %v", err)
				return fmt.Errorf("failed to create MSK Serverless producer for transactions: %v", err)
			}
		} else {
			txProducer, err = kafka.NewAsyncTransactionProducer(cfg.Kafka.Brokers, cfg.Kafka.Topic)
			if err != nil {
				log.Printf("ERROR: Failed to create Kafka producer for transactions: %v", err)
				return fmt.Errorf("failed to create Kafka producer for transactions: %v", err)
			}
		}
		defer txProducer.Close()
	} else {
		log.Println("No Kafka Topic configured, transactions will not be sent to Kafka.")
	}

	// 如果需要向gRPC请求添加token，设置metadata
	grpcCtx := ctx
	if cfg.GRPC.Token != "" {
		grpcCtx = metadata.NewOutgoingContext(ctx, metadata.New(map[string]string{"x-token": cfg.GRPC.Token}))
	}

	var stream pb.Geyser_SubscribeClient
	var err error

	// 设置流并发送初始订阅的函数
	setupStream := func() error {
		log.Println("Setting up new gRPC stream...")
		stream, err = client.Subscribe(grpcCtx)
		if err != nil {
			log.Printf("Failed to create gRPC subscribe stream: %v", err)
			return err
		}

		subscription := buildSubscriptionRequest(cfg)

		// 调试输出订阅请求（可选）
		if log.Flags()&log.Lshortfile != 0 { // 如果日志级别较高
			subscriptionJson, _ := json.Marshal(&subscription)
			log.Printf("Sending subscription request: %s", string(subscriptionJson))
		}

		err = stream.Send(&subscription)
		if err != nil {
			log.Printf("Failed to send subscription request: %v", err)
			return err
		}
		log.Println("Subscription request sent successfully.")
		return nil
	}

	// 初始化流
	if err := setupStream(); err != nil {
		log.Printf("Initial stream setup failed, exiting gRPC subscription: %v", err)
		return fmt.Errorf("initial stream setup failed: %v", err)
	}

	// 初始化工作池
	var workerPool *WorkerPool
	if txProducer != nil {
		workerCount := cfg.WorkerPool.Size
		if workerCount <= 0 {
			workerCount = defaultWorkerPoolSize
		}
		workerPool = NewWorkerPool(ctx, workerCount, txProducer)
		defer workerPool.Shutdown()
	}

recvLoop:
	for {
		select {
		case <-ctx.Done():
			log.Println("gRPC subscription: context cancelled, exiting receive loop.")
			break recvLoop // 退出外层循环
		case <-models.ResubTrigger:
			subscription := buildSubscriptionRequest(cfg)
			if stream == nil {
				if err := setupStream(); err != nil {
					log.Printf("Failed to re-establish stream on trigger: %v", err)
					continue
				}
			} else {
				err := stream.Send(&subscription)
				if err != nil {
					log.Printf("Failed to send updated subscription request: %v. Resetting stream.", err)
					stream.CloseSend()
					stream = nil
					continue
				}
			}
		default:
			// 检查流是否需要重新建立
			if stream == nil {
				log.Println("Stream is nil, attempting to re-establish before receiving...")
				if err := setupStream(); err != nil {
					log.Printf("Failed to re-establish stream: %v. Retrying after delay.", err)
					select {
					case <-time.After(5 * time.Second):
					case <-ctx.Done():
						log.Println("Context cancelled while waiting to retry stream setup.")
						break recvLoop
					}
					continue
				}
			}

			// 从流中接收更新
			resp, err := stream.Recv()
			if err != nil {
				if err == io.EOF {
					log.Println("gRPC stream closed by server (EOF).")
				} else {
					log.Printf("Error receiving gRPC update: %v. Resetting stream.", err)
				}
				stream.CloseSend()
				stream = nil
				continue
			}

			// 处理接收到的不同类型的更新
			handleGRPCUpdate(resp, txProducer, workerPool)
		}
	}

	// 关闭流（如果仍然打开）
	if stream != nil {
		stream.CloseSend()
	}

	return nil
}

// handleGRPCUpdate 处理从gRPC接收到的更新
func handleGRPCUpdate(resp *pb.SubscribeUpdate, txProducer *kafka.AsyncTransactionProducer, workerPool *WorkerPool) {
	if tx := resp.GetTransaction(); tx != nil || resp.GetTransactionStatus() != nil {
		if workerPool != nil {
			workerPool.Submit(resp)
		} else {
			handleRawUpdate(resp, txProducer)
		}
	} else {
		return
	}
}

// handleRawUpdate 处理原始更新并直接发送到Kafka
func handleRawUpdate(update *pb.SubscribeUpdate, producer *kafka.AsyncTransactionProducer) {
	atomic.AddUint64(&receivedTxCount, 1)

	if producer == nil {
		log.Printf("警告: 交易生产者未初始化，无法发送原始更新")
		return
	}

	// 获取签名和槽位信息，用作消息键
	var signature string
	var slot uint64

	if tx := update.GetTransaction(); tx != nil && tx.Transaction != nil && tx.Transaction.Signature != nil {
		signature = solana.SignatureFromBytes(tx.Transaction.Signature).String()
		slot = tx.Slot
	} else if txStatus := update.GetTransactionStatus(); txStatus != nil && txStatus.Signature != nil {
		signature = solana.SignatureFromBytes(txStatus.Signature).String()
		slot = txStatus.Slot
		atomic.AddUint64(&receivedStatusCount, 1)
	} else {
		log.Printf("警告: 接收到不包含签名的更新")
		return
	}

	// 将protobuf消息转为二进制数据
	protoBytes, err := proto.Marshal(update)
	if err != nil {
		log.Printf("错误: 无法序列化更新数据: %v", err)
		return
	}

	// 直接异步发送原始protobuf数据到Kafka
	producer.SendRawBytes(signature, slot, protoBytes)
	atomic.AddUint64(&sentTxCount, 1)

	// 如果是交易状态更新，增加状态发送计数
	if update.GetTransactionStatus() != nil {
		atomic.AddUint64(&sentStatusCount, 1)
	}
}

//// handleAccountUpdate 处理账户更新
//func handleAccountUpdate(account *pb.SubscribeUpdateAccount, producer *kafka.ActiveAddressProducer) {
//	pubkey := solana.PublicKeyFromBytes(account.Account.Pubkey)
//	pubkeyStr := pubkey.String()
//	ownerStr := solana.PublicKeyFromBytes(account.Account.Owner).String()
//
//	log.Printf("接收到账户更新: 地址=%s, lamports=%d, owner=%s, slot=%d",
//		pubkeyStr,
//		account.Account.Lamports,
//		ownerStr,
//		account.Slot)
//
//	// 如果配置了活跃地址的Kafka生产者，将地址发送到Kafka
//	if producer != nil {
//		addressInfo := models.AddressInfo{
//			Address:    pubkeyStr,
//			Owner:      ownerStr,
//			Lamports:   account.Account.Lamports,
//			Slot:       account.Slot,
//			Executable: account.Account.Executable,
//			Timestamp:  time.Now().UnixNano() / 1e6, // 转换为毫秒
//		}
//
//		if err := producer.SendAddressInfo(pubkeyStr, addressInfo); err != nil {
//			log.Printf("Error sending address to Kafka: %v", err)
//		}
//	}
//}
