package grpc

import (
	"chain-quote-server/pkg/kafka"
	pb "chain-quote-server/proto"
	"context"
	"log"
	"sync"
)

// 工作池配置
const (
	defaultWorkerPoolSize = 10 // 默认工作池大小
	maxWorkerPoolSize     = 40 // 最大工作池大小
)

// WorkerPool 工作池结构
type WorkerPool struct {
	workers    int
	tasks      chan *pb.SubscribeUpdate
	wg         sync.WaitGroup
	ctx        context.Context
	cancel     context.CancelFunc
	txProducer *kafka.AsyncTransactionProducer
}

// NewWorkerPool 创建新的工作池
func NewWorkerPool(ctx context.Context, workerCount int, txProducer *kafka.AsyncTransactionProducer) *WorkerPool {
	// 限制工作池大小在合理范围内
	if workerCount <= 0 {
		workerCount = defaultWorkerPoolSize
	} else if workerCount > maxWorkerPoolSize {
		workerCount = maxWorkerPoolSize
	}

	ctx, cancel := context.WithCancel(ctx)
	pool := &WorkerPool{
		workers:    workerCount,
		tasks:      make(chan *pb.SubscribeUpdate, workerCount*2), // 缓冲区大小为worker数量的2倍
		ctx:        ctx,
		cancel:     cancel,
		txProducer: txProducer,
	}

	// 启动工作协程
	pool.start()
	return pool
}

// start 启动工作池
func (p *WorkerPool) start() {
	p.wg.Add(p.workers)
	for i := 0; i < p.workers; i++ {
		go p.worker(i)
	}
}

// worker 工作协程
func (p *WorkerPool) worker(id int) {
	defer p.wg.Done()
	for {
		select {
		case <-p.ctx.Done():
			log.Printf("Worker %d shutting down", id)
			return
		case task, ok := <-p.tasks:
			if !ok {
				return
			}
			// 处理任务
			handleRawUpdate(task, p.txProducer)
		}
	}
}

// Submit 提交任务到工作池（阻塞直到有空位）
func (p *WorkerPool) Submit(update *pb.SubscribeUpdate) {
	select {
	case <-p.ctx.Done():
		log.Printf("Worker pool is shutting down, task rejected")
	case p.tasks <- update:
		// 任务已提交（阻塞直到有空位）
	}
}

// Shutdown 关闭工作池
func (p *WorkerPool) Shutdown() {
	p.cancel()
	close(p.tasks)
	p.wg.Wait()
	log.Println("Worker pool shutdown complete")
}
