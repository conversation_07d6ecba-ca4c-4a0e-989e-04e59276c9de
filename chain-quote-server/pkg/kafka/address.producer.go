package kafka

//
//import (
//	"chain-quote-server/pkg/models"
//	"encoding/json"
//	"github.com/IBM/sarama"
//	"log"
//)
//
//// ActiveAddressProducer 处理向Kafka发送活跃地址信息
//type ActiveAddressProducer struct {
//	Producer sarama.SyncProducer
//	Topic    string
//}
//
//// NewActiveAddressProducer 创建一个新的活跃地址Kafka生产者
//func NewActiveAddressProducer(brokers []string, topic string) (*ActiveAddressProducer, error) {
//	config := sarama.NewConfig()
//	config.Producer.RequiredAcks = sarama.WaitForAll
//	config.Producer.Retry.Max = 5
//	config.Producer.Return.Successes = true
//
//	producer, err := sarama.NewSyncProducer(brokers, config)
//	if err != nil {
//		return nil, err
//	}
//
//	return &ActiveAddressProducer{
//		Producer: producer,
//		Topic:    topic,
//	}, nil
//}
//
//// SendAddressInfo 将地址信息发送到Kafka，使用地址作为消息键
//func (p *ActiveAddressProducer) SendAddressInfo(address string, info models.AddressInfo) error {
//	// 将地址信息序列化为JSON
//	jsonData, err := json.Marshal(info)
//	if err != nil {
//		return err
//	}
//
//	// 创建Kafka消息，使用地址作为键
//	msg := &sarama.ProducerMessage{
//		Topic: p.Topic,
//		Key:   sarama.StringEncoder(address),
//		Value: sarama.ByteEncoder(jsonData),
//	}
//
//	// 发送消息
//	_, _, err = p.Producer.SendMessage(msg)
//	if err != nil {
//		log.Printf("Error sending address %s to Kafka: %v", address, err)
//		return err
//	}
//	return nil
//}
//
//// Close 关闭Kafka生产者
//func (p *ActiveAddressProducer) Close() error {
//	if p.Producer != nil {
//		return p.Producer.Close()
//	}
//	return nil
//}
