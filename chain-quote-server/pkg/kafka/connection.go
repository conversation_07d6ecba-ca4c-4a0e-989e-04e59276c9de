package kafka

import (
	"log"
	"time"

	kafkago "github.com/segmentio/kafka-go"
)

// NewMSKServerlessProducer creates a new producer for AWS MSK Serverless (segmentio/kafka-go version)
func NewMSKServerlessProducer(brokers []string, topic, region string) (*AsyncTransactionProducer, error) {
	writer := &kafkago.Writer{
		Addr:         kafkago.TCP(brokers...),
		Topic:        topic,
		Balancer:     &kafkago.Hash{},
		BatchSize:    100,
		BatchTimeout: 100 * time.Millisecond,
		Async:        true,
		RequiredAcks: kafkago.RequireAll,
		Compression:  kafkago.Snappy,
		// 如需 TLS/SASL，可在 Dialer 字段配置
	}

	ap := &AsyncTransactionProducer{
		writer:     writer,
		topic:      topic,
		closeCh:    make(chan struct{}),
		lastReport: time.Now(),
	}

	// 启动统计 goroutine（实际统计已在 transaction.producer.go 里做了）
	ap.wg.Add(1)
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer func() {
			ticker.Stop()
			ap.wg.Done()
		}()
		for {
			select {
			case <-ticker.C:
				// 可在此打印统计信息（实际统计已在 transaction.producer.go 里做了）
			case <-ap.closeCh:
				return
			}
		}
	}()

	log.Printf("MSK Serverless Kafka producer started, sending messages to topic: %s", topic)
	return ap, nil
}
