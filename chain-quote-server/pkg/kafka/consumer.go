package kafka

//
//import (
//	"context"
//	"crypto/tls"
//	"encoding/json"
//	"log"
//	"os"
//	"strings"
//	"sync"
//	"time"
//
//	"chain-quote-server/pkg/storage"
//
//	"github.com/IBM/sarama"
//)
//
//// MessageHandler 是处理消息的回调函数类型
//type MessageHandler func(topic string, partition int32, offset int64, key, value []byte) error
//
//// ConsumerGroup 管理Kafka消费者组
//type ConsumerGroup struct {
//	client   sarama.ConsumerGroup
//	topics   []string
//	handler  MessageHandler
//	ctx      context.Context
//	cancel   context.CancelFunc
//	wg       sync.WaitGroup
//	ready    chan bool
//	shutdown chan os.Signal
//}
//
//// Consumer 实现sarama.ConsumerGroupHandler接口
//type Consumer struct {
//	handler   MessageHandler
//	ready     chan bool
//	retryWait chan bool
//}
//
//// Setup 在消费者会话开始时调用
//func (consumer *Consumer) Setup(sarama.ConsumerGroupSession) error {
//	// 标记消费者已准备好
//	close(consumer.ready)
//	return nil
//}
//
//// Cleanup 在消费者会话结束时调用
//func (consumer *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
//	return nil
//}
//
//// ConsumeClaim 处理每个分区的消息
//func (consumer *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
//	// 处理每个消息
//	for message := range claim.Messages() {
//		// 调用消息处理函数
//		if err := consumer.handler(message.Topic, message.Partition, message.Offset, message.Key, message.Value); err != nil {
//			log.Printf("处理消息时发生错误: %v", err)
//		}
//
//		// 标记消息已处理
//		session.MarkMessage(message, "")
//	}
//
//	return nil
//}
//
//// NewConsumerGroup 创建一个新的消费者组
//func NewConsumerGroup(brokers []string, groupID string, topics []string, handler MessageHandler) (*ConsumerGroup, error) {
//	config := sarama.NewConfig()
//	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
//	config.Consumer.Offsets.Initial = sarama.OffsetOldest
//
//	// 创建消费者组客户端
//	client, err := sarama.NewConsumerGroup(brokers, groupID, config)
//	if err != nil {
//		return nil, err
//	}
//
//	ctx, cancel := context.WithCancel(context.Background())
//
//	// 不再处理操作系统信号
//	signals := make(chan os.Signal, 1)
//	// 移除信号通知
//
//	return &ConsumerGroup{
//		client:   client,
//		topics:   topics,
//		handler:  handler,
//		ctx:      ctx,
//		cancel:   cancel,
//		ready:    make(chan bool),
//		shutdown: signals,
//	}, nil
//}
//
//// NewMSKServerlessConsumerGroup 创建一个 AWS MSK Serverless 的消费者组
//func NewMSKServerlessConsumerGroup(brokers []string, groupID string, topics []string, handler MessageHandler, region string) (*ConsumerGroup, error) {
//	config := sarama.NewConfig()
//	config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
//	config.Consumer.Offsets.Initial = sarama.OffsetOldest
//
//	// AWS MSK Serverless 认证配置
//	config.Net.SASL.Enable = true
//	config.Net.SASL.Mechanism = sarama.SASLTypeOAuth
//	// config.Net.SASL.TokenProvider = &MSKAccessTokenProvider{region: region}
//
//	// TLS 配置
//	config.Net.TLS.Enable = true
//	config.Net.TLS.Config = &tls.Config{}
//
//	// 连接超时设置
//	config.Net.DialTimeout = 30 * time.Second
//	config.Net.ReadTimeout = 30 * time.Second
//	config.Net.WriteTimeout = 30 * time.Second
//
//	// 创建消费者组客户端
//	client, err := sarama.NewConsumerGroup(brokers, groupID, config)
//	if err != nil {
//		return nil, err
//	}
//
//	ctx, cancel := context.WithCancel(context.Background())
//
//	signals := make(chan os.Signal, 1)
//
//	return &ConsumerGroup{
//		client:   client,
//		topics:   topics,
//		handler:  handler,
//		ctx:      ctx,
//		cancel:   cancel,
//		ready:    make(chan bool),
//		shutdown: signals,
//	}, nil
//}
//
//// Start 启动消费者组
//func (c *ConsumerGroup) Start() error {
//	log.Printf("Kafka 消费者组启动，消费 topics: %v", c.topics)
//
//	c.wg.Add(1)
//	go func() {
//		defer c.wg.Done()
//
//		for {
//			// 检查是否收到关闭信号
//			select {
//			case <-c.ctx.Done():
//				log.Println("终止Kafka消费者：收到上下文取消")
//				return
//			case s := <-c.shutdown:
//				// 这个分支几乎不会执行，保留用于兼容性
//				log.Printf("终止Kafka消费者：收到信号 %s", s)
//				c.cancel()
//				return
//			default:
//				// 继续执行
//			}
//
//			// 创建消费者实例
//			consumer := Consumer{
//				handler:   c.handler,
//				ready:     make(chan bool),
//				retryWait: make(chan bool),
//			}
//
//			// 消费消息
//			err := c.client.Consume(c.ctx, c.topics, &consumer)
//			if err != nil {
//				if err == c.ctx.Err() {
//					// 上下文取消
//					return
//				}
//				log.Printf("Kafka消费错误: %v", err)
//				// 等待重试
//				select {
//				case <-time.After(5 * time.Second):
//				case <-c.ctx.Done():
//					return
//				}
//			}
//
//			// 检查消费者组是否仍然正常
//			if c.ctx.Err() != nil {
//				return
//			}
//
//			// 重置ready通道
//			c.ready = make(chan bool)
//		}
//	}()
//
//	// 等待消费者准备就绪
//	<-c.ready
//	return nil
//}
//
//// Stop 停止消费者组
//func (c *ConsumerGroup) Stop() {
//	c.cancel()
//	c.wg.Wait()
//	if err := c.client.Close(); err != nil {
//		log.Printf("关闭Kafka消费者组时出错: %v", err)
//	}
//}
//
//// ProcessAddressUpdateMessage 处理地址更新消息
//// 消息格式:
//// - 键: userid-groupid
//// - 值: 地址数组 JSON
//func ProcessAddressUpdateMessage(key, value []byte) (bool, error) {
//	// 检查键
//	if len(key) == 0 {
//		return false, nil
//	}
//
//	// 解析键 (格式: userid-groupid)
//	keyStr := string(key)
//	parts := strings.Split(keyStr, "-")
//	if len(parts) != 2 {
//		return false, nil
//	}
//
//	userID := parts[0]
//	groupID := parts[1]
//
//	// 检查值是否为JSON数组
//	if len(value) == 0 || value[0] != '[' {
//		return false, nil
//	}
//
//	// 解析地址数组
//	var addresses []string
//	if err := json.Unmarshal(value, &addresses); err != nil {
//		return false, err
//	}
//
//	// 处理地址更新
//	var changed bool
//
//	//if groupID == "0" {
//	//	// 清空用户的所有地址
//	//	changed = storage.ClearUserAddresses(userID)
//	//} else if len(addresses) == 0 {
//	//	// 删除指定分组的地址
//	//	changed = storage.RemoveGroupAddresses(userID, groupID)
//	//} else {
//	//	// 更新指定分组的地址
//	//	changed = storage.UpdateGroupAddresses(userID, groupID, addresses)
//	//}
//
//	return changed, nil
//}
