package kafka

import (
	"context"
	"log"
	"sync"
	"sync/atomic"
	"time"

	kafkago "github.com/segmentio/kafka-go"
)

// AsyncTransactionProducer 异步处理向Kafka发送交易信息（segmentio/kafka-go 实现）
type AsyncTransactionProducer struct {
	writer        *kafkago.Writer
	topic         string
	wg            sync.WaitGroup
	closeCh       chan struct{}
	msgCount      int64 // 成功发送累计
	errCount      int64 // 失败累计
	dropCount     int64 // 丢弃累计
	lastReport    time.Time
	lastMsgCount  int64
	lastErrCount  int64
	lastDropCount int64
}

// NewAsyncTransactionProducer 创建一个新的异步交易Kafka生产者（segmentio/kafka-go 实现）
func NewAsyncTransactionProducer(brokers []string, topic string) (*AsyncTransactionProducer, error) {
	writer := &kafkago.Writer{
		Addr:         kafkago.TCP(brokers...),
		Topic:        topic,
		Balancer:     &kafkago.Hash{},
		BatchSize:    100,
		BatchTimeout: 100 * time.Millisecond,
		Async:        true,
		RequiredAcks: kafkago.RequireAll,
		Compression:  kafkago.Snappy,
	}

	ap := &AsyncTransactionProducer{
		writer:     writer,
		topic:      topic,
		closeCh:    make(chan struct{}),
		lastReport: time.Now(),
	}

	// 启动统计 goroutine
	ap.wg.Add(1)
	go func() {
		ticker := time.NewTicker(30 * time.Second)
		defer func() {
			ticker.Stop()
			ap.wg.Done()
		}()
		for {
			select {
			case <-ticker.C:
				sent := atomic.LoadInt64(&ap.msgCount)
				failed := atomic.LoadInt64(&ap.errCount)
				dropped := atomic.LoadInt64(&ap.dropCount)
				deltaSent := sent - ap.lastMsgCount
				deltaFailed := failed - ap.lastErrCount
				deltaDropped := dropped - ap.lastDropCount
				log.Printf("[Kafka统计] 30s内: 成功=%d, 失败=%d, 丢弃=%d | 累计: 成功=%d, 失败=%d, 丢弃=%d | 速率=%.2f msg/s",
					deltaSent, deltaFailed, deltaDropped, sent, failed, dropped, float64(deltaSent)/30.0)
				ap.lastMsgCount = sent
				ap.lastErrCount = failed
				ap.lastDropCount = dropped
			case <-ap.closeCh:
				return
			}
		}
	}()

	return ap, nil
}

// SendRawBytes 由 WorkerPool 控制并发，直接同步写入 Kafka
func (ap *AsyncTransactionProducer) SendRawBytes(signature string, slot uint64, data []byte) {
	msg := kafkago.Message{
		Key:   []byte(signature),
		Value: data,
		Time:  time.Now(),
	}
	// 直接同步写入，由 WorkerPool 控制并发
	err := ap.writer.WriteMessages(context.Background(), msg)
	if err != nil {
		atomic.AddInt64(&ap.errCount, 1)
		log.Printf("Produce error: %v", err)
	} else {
		atomic.AddInt64(&ap.msgCount, 1)
	}
}

// Close 关闭异步Kafka生产者
func (ap *AsyncTransactionProducer) Close() error {
	close(ap.closeCh)
	ap.wg.Wait()
	return ap.writer.Close()
}

// 辅助函数：将 []string brokers 转为逗号分隔字符串（不再需要，保留兼容）
func brokersToString(brokers []string) string {
	if len(brokers) == 0 {
		return ""
	}
	res := brokers[0]
	for i := 1; i < len(brokers); i++ {
		res += "," + brokers[i]
	}
	return res
}
