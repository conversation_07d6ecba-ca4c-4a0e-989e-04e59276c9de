package models

import (
	"encoding/json"
	"sync"

	"time"

	"google.golang.org/grpc/keepalive"
)

// 通用的变量和结构
var (
	// GRPCKeepaliveParams - gRPC 客户端保活配置
	GRPCKeepaliveParams = keepalive.ClientParameters{
		Time:                10 * time.Second,
		Timeout:             time.Second,
		PermitWithoutStream: true,
	}

	// ResubTrigger - 用于触发gRPC重新订阅的通道
	ResubTrigger = make(chan struct{}, 1) // 缓冲通道，避免阻塞发送者
)

// AddressInfo 发送到Kafka的地址信息结构
type AddressInfo struct {
	Address    string `json:"address"`
	Owner      string `json:"owner"`
	Lamports   uint64 `json:"lamports"`
	Slot       uint64 `json:"slot"`
	Executable bool   `json:"executable"`
	Timestamp  int64  `json:"timestamp"`
}

// TransactionInfo 发送到Kafka的交易信息结构
type TransactionInfo struct {
	Signature   string   `json:"signature"`
	Slot        uint64   `json:"slot"`
	BlockTime   int64    `json:"block_time,omitempty"`
	IsVote      bool     `json:"is_vote"`
	Error       string   `json:"error,omitempty"`
	Fee         uint64   `json:"fee"`
	Status      string   `json:"status"`
	Accounts    []string `json:"accounts"`
	LogMessages []string `json:"log_messages,omitempty"`
	Timestamp   int64    `json:"timestamp"`
}

// RawTransactionData 存储原始交易数据，用于直接发送到Kafka
type RawTransactionData struct {
	Signature string          `json:"signature"` // 交易签名
	Slot      uint64          `json:"slot"`      // 交易所在区块槽位
	RawData   json.RawMessage `json:"raw_data"`  // 原始交易数据
	Timestamp int64           `json:"timestamp"` // 处理时间戳（毫秒）
}

// AddressStorage 线程安全的地址存储
// 结构: map[UserID]map[GroupID][]Address
type AddressStorage struct {
	sync.RWMutex
	Data map[string]map[string][]string
}

//// NewAddressStorage 创建新的地址存储实例
//func NewAddressStorage() *AddressStorage {
//	return &AddressStorage{
//		Data: make(map[string]map[string][]string),
//	}
//}
