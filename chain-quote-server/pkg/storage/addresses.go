package storage

//
//import (
//	"chain-quote-server/pkg/models"
//)
//
//// GlobalAddressStorage 全局地址存储实例
//var GlobalAddressStorage = models.NewAddressStorage()
//
//// GetAllMonitoredAddresses 获取所有被监控的地址列表
//func GetAllMonitoredAddresses() []string {
//	GlobalAddressStorage.RLock()
//	defer GlobalAddressStorage.RUnlock()
//
//	uniqueAddresses := make(map[string]struct{})
//	for _, groups := range GlobalAddressStorage.Data {
//		for _, addresses := range groups {
//			for _, addr := range addresses {
//				uniqueAddresses[addr] = struct{}{}
//			}
//		}
//	}
//
//	addressList := make([]string, 0, len(uniqueAddresses))
//	for addr := range uniqueAddresses {
//		addressList = append(addressList, addr)
//	}
//
//	// 如果需要添加特定的硬编码地址，可以在这里添加
//	// addressList = append(addressList, "特定的硬编码地址")
//
//	return addressList
//}
//
//// ClearUserAddresses 清除特定用户的所有地址
//func ClearUserAddresses(userID string) bool {
//	GlobalAddressStorage.Lock()
//	defer GlobalAddressStorage.Unlock()
//
//	if _, exists := GlobalAddressStorage.Data[userID]; exists {
//		delete(GlobalAddressStorage.Data, userID)
//		return true
//	}
//
//	return false
//}
//
//// RemoveGroupAddresses 移除特定用户组的地址
//func RemoveGroupAddresses(userID, groupID string) bool {
//	GlobalAddressStorage.Lock()
//	defer GlobalAddressStorage.Unlock()
//
//	if _, userExists := GlobalAddressStorage.Data[userID]; !userExists {
//		return false
//	}
//
//	if _, groupExists := GlobalAddressStorage.Data[userID][groupID]; !groupExists {
//		return false
//	}
//
//	delete(GlobalAddressStorage.Data[userID], groupID)
//
//	// 如果用户没有组了，删除用户条目
//	if len(GlobalAddressStorage.Data[userID]) == 0 {
//		delete(GlobalAddressStorage.Data, userID)
//	}
//
//	return true
//}
//
//// UpdateGroupAddresses 更新特定用户组的地址
//func UpdateGroupAddresses(userID, groupID string, addresses []string) bool {
//	GlobalAddressStorage.Lock()
//	defer GlobalAddressStorage.Unlock()
//
//	if _, userExists := GlobalAddressStorage.Data[userID]; !userExists {
//		GlobalAddressStorage.Data[userID] = make(map[string][]string)
//	}
//
//	// 检查是否真正需要更新（地址列表发生变化）
//	needUpdate := true
//	if oldAddresses, ok := GlobalAddressStorage.Data[userID][groupID]; ok {
//		if len(oldAddresses) == len(addresses) {
//			needUpdate = false
//			// 检查每个地址是否相同
//			for i, addr := range oldAddresses {
//				if addr != addresses[i] {
//					needUpdate = true
//					break
//				}
//			}
//		}
//	}
//
//	if needUpdate {
//		GlobalAddressStorage.Data[userID][groupID] = addresses
//		return true
//	}
//
//	return false
//}
