# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOINSTALL=$(GOCMD) install
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOMODTIDY=$(GOCMD) mod tidy
BINARY_NAME=model-processor
BINARY_UNIX=$(BINARY_NAME)_unix

# Default target
all: build

# Build for Linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v .

# Build for current platform
build:
	$(GOBUILD) -o $(BINARY_NAME) -v .

# Clean build files
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# Run tests
test:
	$(GOTEST) -v ./...

# Tidy dependencies (replaces old deps target)
tidy:
	$(GOMODTIDY)

# Run the application
# Allows passing arguments like: make run ARGS="--config=./config.yaml"
run: build
	./$(BINARY_NAME) $(ARGS)

# Build and run for Linux
run-linux: build-linux
	./$(BINARY_UNIX) $(ARGS)

.PHONY: all build build-linux clean test tidy run run-linux
