# Solana Transaction Processor

A Golang application that consumes Solana transaction updates from Kafka, parses swap instructions, and stores relevant data into a TDengine database. Now includes a WebSocket server for real-time data streaming.

## Features

- Consumes Protobuf-encoded Solana transaction updates from a Kafka topic.
- Parses transaction details, including swap instructions for various protocols (Raydium, Pump.fun, etc.) using the `txparser` package.
- Transforms parsed data into a structured format (`model.MemeTx`).
- Stores processed transaction data into a TDengine stable table (`meme_tx`) using child tables per token mint address (`tx_<mint_addr>`).
- **Now with WebSocket support**: Stream real-time transaction data to clients, with subscription capabilities.
- Configurable via `config.yaml` or environment variables.
- Handles graceful shutdown.

## Prerequisites

- Go (see `.go-version` or `go.mod` for required version, likely 1.21+)
- Access to a Kafka cluster.
- Access to a TDengine database server (v3+).
- The `meme_tx` stable table created in TDengine (see `scripts/alter_table.sql`).
- gorilla/websocket package: `go get github.com/gorilla/websocket`

## Installation

1.  **Clone the repository:**
    ```bash
    git clone <your-repo-url>
    cd model-processer
    ```
2.  **Install dependencies:**
    ```bash
    go mod tidy
    ```

## Configuration

Configuration is handled by the `config` package and loaded from `config.yaml` by default. Settings can be overridden by environment variables.

**`config.yaml`:**

```yaml
# Example config.yaml
kafka:
  brokers: ["localhost:9092"] # List of Kafka broker addresses
  topic: "solana-transactions"  # Kafka topic to consume from
  groupID: "model-processor-group-1" # Kafka consumer group ID
  # autoOffsetReset: "earliest" # or "latest"

tdengine:
  host: "127.0.0.1" # TDengine host
  port: "6041"      # TDengine RESTful API port
  user: "root"      # TDengine username
  password: "taosdata"  # TDengine password
  database: "solana_data" # TDengine database name

log:
  level: "info" # Log level (e.g., "debug", "info", "warn", "error")

# WebSocket服务器配置
websocket:
  enabled: true  # 设置为false可以禁用WebSocket服务器
  host: "0.0.0.0"  # 监听地址，0.0.0.0表示所有网络接口
  port: "8080"     # WebSocket服务端口
  read_buffer_size: 1024  # 读缓冲区大小（字节）
  write_buffer_size: 1024 # 写缓冲区大小（字节）
  check_origin: false     # 设置为true可以限制跨域请求
```

**Environment Variables:**

Environment variables override `config.yaml` values. They are prefixed and use underscores:

- `KAFKA_BROKERS`: Comma-separated list (e.g., `"broker1:9092,broker2:9092"`)
- `KAFKA_TOPIC`
- `KAFKA_GROUPID`
- `TDENGINE_HOST`
- `TDENGINE_PORT`
- `TDENGINE_USER`
- `TDENGINE_PASSWORD`
- `TDENGINE_DATABASE`
- `LOG_LEVEL`
- `WEBSOCKET_ENABLED`
- `WEBSOCKET_HOST`
- `WEBSOCKET_PORT`
- `WEBSOCKET_READ_BUFFER_SIZE`
- `WEBSOCKET_WRITE_BUFFER_SIZE`
- `WEBSOCKET_CHECK_ORIGIN`

## Building

```bash
go build -o model-processor main.go
```
This creates an executable named `model-processor` in the current directory.

## Running

Ensure Kafka and TDengine are running and accessible, and the `meme_tx` table exists in TDengine.

```bash
./model-processor
```

The application will connect to Kafka, start consuming messages from the configured topic, process them, and insert data into TDengine. If WebSocket is enabled, it will also start a WebSocket server to stream transaction data to clients. Use `Ctrl+C` to trigger a graceful shutdown.

## WebSocket API

The WebSocket API provides real-time transaction updates and historical data querying capabilities.

### Connect to WebSocket Server

```
ws://your-server:8080/ws
```

### Subscribe to Transaction Updates

To subscribe to updates for a specific token mint address, send a JSON message:

```json
{
  "action": "subscribe",
  "topic": "TOKEN_MINT_ADDRESS"
}
```

You can also subscribe to transaction types:

```json
{
  "action": "subscribe",
  "topic": "type:BUY"
}
```

Or to a specific network:

```json
{
  "action": "subscribe",
  "topic": "network:solana"
}
```

### Unsubscribe

To unsubscribe from a topic:

```json
{
  "action": "unsubscribe",
  "topic": "TOKEN_MINT_ADDRESS"
}
```

### Transaction Format

When a transaction matching your subscription occurs, you'll receive a JSON message:

```json
{
  "mint_addr": "TOKEN_MINT_ADDRESS",
  "tx_hash": "TRANSACTION_HASH",
  "user_addr": "USER_ADDRESS",
  "token_in_addr": "TOKEN_IN_ADDRESS",
  "token_out_addr": "TOKEN_OUT_ADDRESS",
  "amount_in": 123.45,
  "amount_out": 678.90,
  "tx_type": "BUY",
  "block_slot": 123456789,
  "timestamp": "2023-04-26T12:34:56Z",
  "network": "solana"
}
```

## Project Structure

```
.
├── config/             # Configuration loading (config.go, config.yaml)
├── kafka/              # Kafka consumer/producer logic
├── model/              # Data models (MemeTx, Token) and DB interactions (tdengine.go)
├── proto/              # Protobuf definitions and generated Go code
├── scripts/            # Utility scripts (e.g., alter_table.sql)
├── service/            # Business logic (transaction_service.go)
├── txparser/           # Solana transaction parsing logic
├── websocket/          # WebSocket server implementation
│   ├── server.go       # WebSocket server core
│   ├── client.go       # Client connection handling
│   └── handler.go      # Transaction data handling
├── main.go             # Application entry point
├── go.mod              # Go module definition
├── go.sum              # Dependency checksums
└── README.md           # This file
```

## Contributing

Please refer to the contribution guidelines (if any). Open an issue or submit a pull request for bugs or features.
