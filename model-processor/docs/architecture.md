# Solana 交易处理器 - 系统架构文档

## 1. 系统概述

Solana 交易处理器是一个高性能的实时交易数据处理系统，专门用于处理 Solana 区块链上的 DEX 交易数据。系统采用微服务架构，支持多种数据库后端，提供实时数据流处理和 WebSocket 推送功能。

### 1.1 核心功能
- 消费 Kafka 中的 Protobuf 编码的 Solana 交易更新
- 解析多种 DEX 协议的交易指令（Raydium、Pump.fun、Jupiter、OKX 等）
- 将解析后的数据存储到时序数据库（TDengine/InfluxDB）
- 提供 WebSocket 实时数据推送服务
- 支持地址和代币监控功能
- 提供详细的统计和监控指标

### 1.2 技术栈
- **语言**: Go 1.21+
- **消息队列**: Apache Kafka
- **数据库**: TDengine v3+ / InfluxDB
- **协议**: Protobuf, WebSocket
- **配置管理**: Viper (YAML/环境变量)

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Kafka Cluster │    │  Model Processor │    │  Time Series DB │
│                 │    │                  │    │                 │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ ┌─────────────┐ │
│ │ TX Topic    │◄────┤ │ TX Service   │ ├────┤ │ TDengine/   │ │
│ └─────────────┘ │    │ └──────────────┘ │    │ │ InfluxDB    │ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    │ └─────────────┘ │
│ │ Addr Topic  │◄────┤ │ Addr Service │ │    └─────────────────┘
│ └─────────────┘ │    │ └──────────────┘ │
│ ┌─────────────┐ │    │ ┌──────────────┐ │    ┌─────────────────┐
│ │ Token Topic │◄────┤ │ Token Service│ │    │  WebSocket      │
│ └─────────────┘ │    │ └──────────────┘ │    │  Clients        │
└─────────────────┘    │ ┌──────────────┐ │    │                 │
                       │ │ Stats Service│ │    │ ┌─────────────┐ │
                       │ └──────────────┘ │    │ │ Real-time   │ │
                       │ ┌──────────────┐ │    │ │ Data Stream │ │
                       │ │ WebSocket    │◄────┤ └─────────────┘ │
                       │ │ Server       │ │    └─────────────────┘
                       │ └──────────────┘ │
                       └──────────────────┘
```

### 2.2 核心组件

#### 2.2.1 应用入口 (main.go)
- **App 结构体**: 管理所有应用依赖和状态
- **初始化流程**: 配置加载 → 数据库初始化 → Kafka 客户端 → 服务初始化
- **优雅关闭**: 支持信号处理和资源清理

#### 2.2.2 配置管理 (config/)
- **多层配置**: 默认值 → 配置文件 → 环境变量
- **配置结构**:
  ```go
  type Config struct {
      Database  DatabaseConfig
      Kafka     KafkaConfig
      TDengine  TDengineConfig
      InfluxDB  InfluxDBConfig
      Log       LogConfig
      WebSocket WebSocketConfig
  }
  ```

#### 2.2.3 Kafka 客户端 (kafka/)
- **多消费者支持**: 支持配置多个并发消费者
- **主题管理**: 输入主题、地址主题、代币主题
- **消息计数**: 实时统计消息处理量
- **错误恢复**: 自动重连和错误处理

#### 2.2.4 数据模型层 (model/)
- **统一接口**: TimeSeriesDB 接口抽象不同数据库
- **数据库管理器**: 支持 TDengine 和 InfluxDB 切换
- **核心数据结构**:
  ```go
  type MemeTx struct {
      Ts               time.Time
      BlockTime        int64
      TxHash           string
      UserAddr         string
      TokenInAddr      string
      TokenOutAddr     string
      AmountIn         string
      AmountOut        string
      TxType           string
      BlockSlot        int64
      InstructionIndex int32
      TxIndex          int64
  }
  ```

## 3. 服务层架构

### 3.1 TransactionService (交易服务)
- **职责**: 处理交易数据解析和存储
- **核心流程**:
  1. 接收 Kafka 消息
  2. 调用 txparser 解析交易
  3. 地址过滤检查
  4. 数据库存储
  5. 代币监控转发
- **并发控制**: 使用信号量限制并发 goroutine 数量
- **统计集成**: 实时统计处理指标

### 3.2 AddressService (地址服务)
- **职责**: 管理需要监控的钱包地址
- **数据结构**:
  - `walletIndex`: 钱包地址 → 组ID 集合
  - `groupWallets`: 组ID → 钱包地址映射
- **操作支持**: 添加、删除、查询钱包地址
- **线程安全**: 使用读写锁保护并发访问

### 3.3 TokenService (代币服务)
- **职责**: 管理需要特别监控的代币地址
- **引用计数**: 支持多个订阅者监控同一代币
- **转发机制**: 将匹配的交易转发到指定 Kafka 主题
- **动态管理**: 支持运行时添加/删除监控代币

### 3.4 StatsService (统计服务)
- **时间窗口统计**: 30秒滚动窗口统计
- **多维度指标**:
  - 交易处理量
  - 数据库操作成功/失败率
  - 解析错误统计
  - Kafka 生产统计
  - 性能指标（处理时间）
- **实时输出**: 定期打印详细统计信息

## 4. 交易解析器 (txparser/)

### 4.1 解析器架构
- **协议支持**: Raydium、Orca、Meteora、Pump.fun、Jupiter、OKX 等
- **指令解析**: 支持外部指令和内部指令解析
- **数据提取**: SPL Token 信息和精度提取

### 4.2 支持的协议
```go
const (
    PROTOCOL_RAYDIUM  = "raydium"
    PROTOCOL_ORCA     = "orca"
    PROTOCOL_METEORA  = "meteora"
    PROTOCOL_PUMPFUN  = "pumpfun"
    PROTOCOL_PUMPSWAP = "pumpswap"
)
```

### 4.3 解析流程
1. **交易预处理**: 提取账户密钥和元数据
2. **指令识别**: 根据程序ID识别协议类型
3. **数据解析**: 解析特定协议的交易数据
4. **事件处理**: 处理交易事件和创建事件
5. **数据转换**: 转换为统一的 SwapInfo 结构

## 5. 数据存储架构

### 5.1 数据库抽象
- **统一接口**: TimeSeriesDB 接口
- **多数据库支持**: TDengine 和 InfluxDB
- **连接池管理**: 支持多线程连接池
- **批量操作**: 支持批量插入优化

### 5.2 TDengine 存储方案
- **超级表结构**: 
  - `solana_transaction_user`: 按用户地址分表
  - `solana_transaction_token`: 按代币地址分表
- **子表命名**: `user_<address>` 和 `token_<address>`
- **标签设计**: 用户地址和代币地址作为标签

### 5.3 InfluxDB 存储方案
- **测量表**: `solana_transaction_user` 和 `solana_transaction_token`
- **标签字段**: user_addr, token_addr, network
- **时间精度**: 毫秒级时间戳
- **批量写入**: 支持批量缓冲和异步写入

## 6. WebSocket 服务架构

### 6.1 WebSocket 服务器
- **实时推送**: 支持交易数据实时推送
- **订阅机制**: 支持按代币地址、交易类型、网络订阅
- **连接管理**: 自动处理连接建立和断开
- **消息格式**: JSON 格式的标准化消息

### 6.2 订阅类型
- **代币订阅**: `{"action": "subscribe", "topic": "TOKEN_MINT_ADDRESS"}`
- **类型订阅**: `{"action": "subscribe", "topic": "type:BUY"}`
- **网络订阅**: `{"action": "subscribe", "topic": "network:solana"}`

## 7. 监控和统计

### 7.1 性能指标
- **处理速度**: 每秒处理的交易数量
- **成功率**: 数据库写入成功率
- **错误率**: 解析错误和处理错误统计
- **延迟指标**: 处理时间分布

### 7.2 业务指标
- **交易统计**: 按协议分类的交易量
- **用户活跃度**: 活跃钱包地址数量
- **代币热度**: 热门代币交易统计
- **网络状态**: 区块高度和同步状态

## 8. 扩展性设计

### 8.1 水平扩展
- **Kafka 分区**: 支持多分区并行处理
- **数据库分片**: 按时间和标签分片存储
- **服务实例**: 支持多实例部署

### 8.2 协议扩展
- **插件化设计**: 新协议解析器可独立开发
- **配置驱动**: 通过配置启用/禁用特定协议
- **版本兼容**: 向后兼容的协议升级机制
