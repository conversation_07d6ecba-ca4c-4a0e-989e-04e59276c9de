# Solana 交易处理器 - 业务逻辑文档

## 1. 业务概述

Solana 交易处理器的核心业务是实时处理 Solana 区块链上的 DEX（去中心化交易所）交易数据，为用户提供精准的交易监控、数据分析和实时推送服务。

### 1.1 业务目标
- **实时性**: 毫秒级的交易数据处理和推送
- **准确性**: 精确解析各种 DEX 协议的交易数据
- **可靠性**: 高可用的数据处理和存储服务
- **可扩展性**: 支持新协议和功能的快速集成

### 1.2 核心业务场景
- **交易监控**: 监控特定钱包地址的交易活动
- **代币追踪**: 跟踪特定代币的交易动态
- **套利分析**: 识别跨 DEX 的价格差异机会
- **风险控制**: 实时检测异常交易行为

## 2. 交易处理业务流程

### 2.1 交易数据接收
```
Kafka Topic → TransactionService.ProcessUpdate()
```

**业务规则**:
- 支持多分区并行消费，提高处理吞吐量
- 每个消费者独立处理，避免单点故障
- 消息去重机制，防止重复处理

### 2.2 交易解析业务逻辑

#### 2.2.1 协议识别
系统支持以下 DEX 协议的自动识别和解析：

| 协议 | 程序ID | 支持功能 |
|------|--------|----------|
| Raydium | 多个程序ID | Swap、流动性操作 |
| Orca | PhoeNiXZ8ByJGLkxNfZRnkUfjvmuYqLR89jjFHGqdXY | Whirlpool 交易 |
| Jupiter | JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4 | 聚合交易路由 |
| Pump.fun | 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P | Meme 代币交易 |
| OKX DEX | 多个路由程序 | 聚合交易 |

#### 2.2.2 交易类型分类
```go
// 交易类型定义
const (
    TX_TYPE_BUY  = "BUY"   // 买入操作
    TX_TYPE_SELL = "SELL"  // 卖出操作
    TX_TYPE_SWAP = "SWAP"  // 一般交换
)
```

**分类逻辑**:
- **BUY**: SOL/USDC → 其他代币
- **SELL**: 其他代币 → SOL/USDC
- **SWAP**: 其他代币间的交换

### 2.3 地址过滤业务逻辑

#### 2.3.1 地址监控机制
```go
type AddressService struct {
    walletIndex  map[string]map[string]struct{} // 钱包 → 组ID集合
    groupWallets map[string]map[string]bool     // 组ID → 钱包映射
}
```

**业务规则**:
- 支持按组管理钱包地址
- 一个钱包可以属于多个组
- 动态添加/删除监控地址
- 引用计数管理，避免误删

#### 2.3.2 过滤策略
1. **白名单模式**: 只处理监控列表中的地址
2. **实时更新**: 支持运行时更新监控列表
3. **组权限**: 不同组可以有不同的访问权限

### 2.4 代币监控业务逻辑

#### 2.4.1 代币订阅机制
```go
type TokenService struct {
    tokenAddresses map[string]string // 代币地址 → 目标地址
    tokenCounts    map[string]int    // 代币地址 → 引用计数
}
```

**业务特性**:
- **引用计数**: 支持多个订阅者监控同一代币
- **动态管理**: 实时添加/删除监控代币
- **转发机制**: 匹配交易自动转发到指定主题

#### 2.4.2 转发策略
```
交易匹配 → 检查监控列表 → 转发到目标主题 → 统计更新
```

## 3. 数据存储业务逻辑

### 3.1 存储策略

#### 3.1.1 双表存储模式
- **用户维度表**: 按用户地址分表存储
- **代币维度表**: 按代币地址分表存储

**业务优势**:
- 查询性能优化
- 数据分布均匀
- 支持并行写入

#### 3.1.2 数据分片策略
```sql
-- TDengine 子表命名规则
user_<user_address>   -- 用户交易表
token_<token_address> -- 代币交易表
```

### 3.2 数据一致性保证

#### 3.2.1 事务处理
- **原子性**: 单个交易的所有相关数据同时写入
- **一致性**: 用户表和代币表数据保持一致
- **隔离性**: 并发写入不会相互影响

#### 3.2.2 错误处理
```go
// 错误处理策略
if dbError := database.InsertMemeTx(tx, network, tokenAddr); dbError != nil {
    stats.IncrementStat("db_error")
    log.Printf("Database insert failed: %v", dbError)
    // 不中断处理流程，继续处理下一条
}
```

## 4. 实时推送业务逻辑

### 4.1 WebSocket 订阅管理

#### 4.1.1 订阅类型
```json
// 代币订阅
{"action": "subscribe", "topic": "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"}

// 交易类型订阅
{"action": "subscribe", "topic": "type:BUY"}

// 网络订阅
{"action": "subscribe", "topic": "network:solana"}
```

#### 4.1.2 消息推送格式
```json
{
  "mint_addr": "TOKEN_MINT_ADDRESS",
  "tx_hash": "TRANSACTION_HASH",
  "user_addr": "USER_ADDRESS",
  "token_in_addr": "TOKEN_IN_ADDRESS",
  "token_out_addr": "TOKEN_OUT_ADDRESS",
  "amount_in": 123.45,
  "amount_out": 678.90,
  "tx_type": "BUY",
  "block_slot": 123456789,
  "timestamp": "2023-04-26T12:34:56Z",
  "network": "solana"
}
```

### 4.2 连接管理
- **自动重连**: 客户端断线自动重连机制
- **心跳检测**: 定期发送心跳包检测连接状态
- **负载均衡**: 支持多个 WebSocket 服务器实例

## 5. 统计和监控业务逻辑

### 5.1 统计指标体系

#### 5.1.1 处理性能指标
```go
type TimeWindowStats struct {
    swapTxCount              uint64 // 交换交易数量
    dbSuccessCount           uint64 // 数据库成功写入数量
    parseErrorCount          uint64 // 解析错误数量
    processErrorCount        uint64 // 处理错误数量
    dbErrorCount             uint64 // 数据库错误数量
    addressFilterCount       uint64 // 地址过滤数量
    tokenForwardCount        uint64 // 代币转发数量
}
```

#### 5.1.2 业务指标
- **交易量统计**: 按协议、代币、用户维度统计
- **活跃度指标**: 活跃用户数、热门代币排行
- **性能指标**: 处理延迟、吞吐量、错误率

### 5.2 监控告警机制

#### 5.2.1 阈值监控
- **处理延迟**: 超过阈值触发告警
- **错误率**: 错误率超过 5% 触发告警
- **数据库连接**: 连接失败立即告警

#### 5.2.2 健康检查
```go
func (s *StatsService) HealthCheck() error {
    // 检查各组件健康状态
    // 检查数据库连接
    // 检查 Kafka 连接
    // 检查处理性能
}
```

## 6. 业务规则和约束

### 6.1 数据质量保证

#### 6.1.1 数据验证规则
- **交易哈希**: 必须是有效的 Base58 编码
- **地址格式**: 必须是有效的 Solana 地址格式
- **金额范围**: 金额必须大于 0
- **时间戳**: 必须在合理的时间范围内

#### 6.1.2 重复数据处理
- **交易去重**: 基于交易哈希和指令索引去重
- **幂等性**: 重复处理同一交易不会产生副作用

### 6.2 业务限制

#### 6.2.1 处理限制
- **并发限制**: 最大 500 个并发 goroutine
- **内存限制**: 批量处理大小限制为 100 条记录
- **时间限制**: 单个交易处理超时时间 30 秒

#### 6.2.2 存储限制
- **数据保留**: TDengine 默认保留 7 天数据
- **表数量**: 每个代币/用户对应一个子表
- **索引策略**: 基于时间戳和标签建立索引

## 7. 异常处理和容错机制

### 7.1 错误分类和处理

#### 7.1.1 可恢复错误
- **网络超时**: 自动重试机制
- **数据库连接失败**: 连接池重建
- **解析失败**: 跳过当前交易，继续处理

#### 7.1.2 不可恢复错误
- **配置错误**: 系统启动失败
- **权限错误**: 记录日志并告警
- **资源耗尽**: 优雅降级处理

### 7.2 容错策略
- **熔断机制**: 连续失败达到阈值时暂停处理
- **降级服务**: 关键路径失败时提供基础服务
- **数据备份**: 重要数据多副本存储
