# Solana 交易处理器 - 数据流文档

## 1. 数据流概述

Solana 交易处理器的数据流设计遵循事件驱动架构，通过 Kafka 消息队列实现解耦，支持高并发、低延迟的实时数据处理。

### 1.1 数据流特点
- **实时性**: 毫秒级数据处理延迟
- **可靠性**: 消息持久化和重试机制
- **可扩展性**: 水平扩展支持
- **容错性**: 多层错误处理和恢复机制

### 1.2 数据流向图

```mermaid
graph TD
    A[Solana Blockchain] --> B[Geyser Plugin]
    B --> C[Kafka Cluster]
    C --> D[Model Processor]
    D --> E[Transaction Parser]
    E --> F[Address Filter]
    F --> G[Token Monitor]
    G --> H[Database Storage]
    G --> I[WebSocket Push]
    
    C --> J[Address Updates]
    C --> K[Token Updates]
    J --> L[Address Service]
    K --> M[Token Service]
    L --> F
    M --> G
    
    H --> N[TDengine/InfluxDB]
    I --> O[WebSocket Clients]
```

## 2. 输入数据流

### 2.1 Kafka 主题结构

#### 2.1.1 交易数据主题 (Input Topic)
```yaml
Topic: solana-transactions
Format: Protobuf
Partitions: 多分区支持并行处理
Key: 交易签名
Value: TransactionUpdate (Protobuf)
```

**数据结构**:
```protobuf
message TransactionUpdate {
  Transaction transaction = 1;
  TransactionStatusMeta meta = 2;
  uint64 slot = 3;
  uint64 index = 4;
}
```

#### 2.1.2 地址更新主题 (Address Topic)
```yaml
Topic: address-updates
Format: JSON
Key: groupId (字符串)
Value: 钱包地址数组
```

**消息示例**:
```json
Key: "group_001"
Value: ["address1", "address2", "address3"]
```

#### 2.1.3 代币监控主题 (Token Topic)
```yaml
Topic: token-updates
Format: 字符串
Key: 操作类型
Value: 代币地址
```

**操作类型**:
- `add` / `subscribe`: 添加监控
- `sub` / `unsubscribe`: 取消监控

### 2.2 数据接收流程

```go
// 消息处理器注册
txMessageHandler := func(messageValue []byte, partitionID int32) error {
    return a.txService.ProcessUpdate(messageValue, partitionID)
}

addrMessageHandler := func(messageKey []byte, messageValue []byte) error {
    return a.addressService.ProcessUpdate(messageKey, messageValue)
}

tokenMessageHandler := func(messageKey []byte, messageValue []byte) error {
    return a.tokenService.ProcessUpdate(messageKey, messageValue)
}
```

## 3. 交易数据处理流程

### 3.1 交易解析阶段

#### 3.1.1 Protobuf 解码
```
原始消息 → Protobuf 解码 → TransactionUpdate 结构体
```

**关键字段提取**:
- 交易签名 (Signature)
- 账户密钥列表 (Account Keys)
- 指令列表 (Instructions)
- 内部指令 (Inner Instructions)
- 交易元数据 (Meta)

#### 3.1.2 协议识别
```go
// 协议识别逻辑
for i, outerInstruction := range p.txInfo.Message.Instructions {
    progID := p.allAccountKeys[outerInstruction.ProgramIdIndex]
    switch {
    case progID.Equals(JUPITER_PROGRAM_ID):
        // Jupiter 聚合交易处理
    case progID.Equals(PUMP_FUN_PROGRAM_ID):
        // Pump.fun 交易处理
    case p.isRaydiumProgram(progID):
        // Raydium 交易处理
    }
}
```

### 3.2 交易数据转换

#### 3.2.1 SwapData 结构
```go
type SwapData struct {
    Type             SwapType    // 协议类型
    TxType           string      // 交易类型 (BUY/SELL)
    InstructionIndex int         // 指令索引
    Data             interface{} // 协议特定数据
}
```

#### 3.2.2 统一数据格式转换
```go
// 转换为统一的 MemeTx 格式
func mapFinalTxToMemeTx(ft *FinalTx, txIndex uint64) model.MemeTx {
    return model.MemeTx{
        Ts:               time.Unix(ft.Ts, 0),
        BlockTime:        ft.Ts,
        TxHash:           ft.TxHash,
        UserAddr:         ft.UserAddr,
        TokenInAddr:      ft.TokenInAddr,
        TokenOutAddr:     ft.TokenOutAddr,
        AmountIn:         fmt.Sprintf("%d", ft.AmountIn),
        AmountOut:        fmt.Sprintf("%d", ft.AmountOut),
        TxType:           ft.TxType,
        BlockSlot:        int64(ft.BlockSlot),
        InstructionIndex: int32(ft.InstructionIndex),
        TxIndex:          int64(txIndex),
    }
}
```

### 3.3 过滤和路由阶段

#### 3.3.1 地址过滤流程
```
交易数据 → 提取用户地址 → 检查监控列表 → 过滤决策
```

```go
// 地址过滤逻辑
if s.addressService != nil && !s.addressService.IsWalletStored(finalTx.UserAddr) {
    s.stats.IncrementStat("address_filter")
    return // 跳过未监控的地址
}
```

#### 3.3.2 代币监控检查
```go
// 代币监控检查
if s.tokenService != nil {
    if targetAddr, exists := s.tokenService.GetTargetAddress(mintAddr); exists {
        // 转发到代币监控主题
        s.tokenService.ForwardTransactionWithTarget(targetAddr, txData)
        s.stats.IncrementStat("token_forward")
    }
}
```

## 4. 数据存储流程

### 4.1 数据库写入策略

#### 4.1.1 双表写入模式
```go
// 同时写入用户表和代币表
go func() {
    defer wg.Done()
    if err := database.InsertMemeTxWithThreadID(memeTx, "solana", finalTx.UserAddr, threadID); err != nil {
        s.stats.IncrementStat("db_error")
    } else {
        s.stats.IncrementStat("db_success")
    }
}()

go func() {
    defer wg.Done()
    if err := database.InsertMemeTxWithThreadID(memeTx, "solana", mintAddr, threadID); err != nil {
        s.stats.IncrementStat("db_error")
    } else {
        s.stats.IncrementStat("db_success")
    }
}()
```

#### 4.1.2 批量写入优化
```go
// TDengine 批量写入
func (td *TDengineDB) InsertMemeTxBatch(tx MemeTx, network string, tokenAddr string, threadID int) error {
    buffer := td.getBatchBuffer(threadID)
    
    buffer.mutex.Lock()
    defer buffer.mutex.Unlock()
    
    // 添加到批量缓冲区
    buffer.userTxs = append(buffer.userTxs, MemeTxWithMeta{
        Tx: tx, Network: network, TokenAddr: tokenAddr, ThreadID: threadID,
    })
    
    // 检查是否需要刷新
    if len(buffer.userTxs) >= td.batchSize {
        return td.flushBatchBuffer(buffer, threadID)
    }
    
    return nil
}
```

### 4.2 数据库表结构

#### 4.2.1 TDengine 表结构
```sql
-- 用户交易超级表
CREATE STABLE solana_transaction_user (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  user_addr     VARCHAR(128)
);

-- 代币交易超级表
CREATE STABLE solana_transaction_token (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  user_addr     VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  token_addr    VARCHAR(128)
);
```

#### 4.2.2 InfluxDB 数据点结构
```go
// 创建 InfluxDB 数据点
func (idb *InfluxDBSimple) createTxPoint(tx MemeTx, network string, tokenAddr string) *write.Point {
    return influxdb2.NewPoint("solana_transaction_token",
        map[string]string{
            "token_addr": tokenAddr,
            "network":    network,
            "tx_type":    tx.TxType,
        },
        map[string]interface{}{
            "block_time":        tx.BlockTime,
            "tx_hash":           tx.TxHash,
            "user_addr":         tx.UserAddr,
            "token_in_addr":     tx.TokenInAddr,
            "token_out_addr":    tx.TokenOutAddr,
            "amount_in":         tx.AmountIn,
            "amount_out":        tx.AmountOut,
            "block_slot":        tx.BlockSlot,
            "instruction_index": tx.InstructionIndex,
            "tx_index":          tx.TxIndex,
        },
        tx.Ts)
}
```

## 5. 输出数据流

### 5.1 WebSocket 推送流程

#### 5.1.1 订阅管理
```go
// WebSocket 订阅消息处理
type SubscriptionMessage struct {
    Action string `json:"action"` // subscribe/unsubscribe
    Topic  string `json:"topic"`  // 订阅主题
}
```

#### 5.1.2 实时推送机制
```go
// 交易回调函数
func (ws *WebSocketServer) onTransaction(tx *model.MemeTx, mintAddr string, network string) {
    message := TransactionMessage{
        MintAddr:     mintAddr,
        TxHash:       tx.TxHash,
        UserAddr:     tx.UserAddr,
        TokenInAddr:  tx.TokenInAddr,
        TokenOutAddr: tx.TokenOutAddr,
        AmountIn:     parseFloat(tx.AmountIn),
        AmountOut:    parseFloat(tx.AmountOut),
        TxType:       tx.TxType,
        BlockSlot:    tx.BlockSlot,
        Timestamp:    tx.Ts.Format(time.RFC3339),
        Network:      network,
    }
    
    ws.broadcastToSubscribers(message)
}
```

### 5.2 Kafka 输出流

#### 5.2.1 代币监控转发
```yaml
Topic: token-output
Key: 目标地址
Value: 交易数据 (JSON)
```

#### 5.2.2 用户交易转发
```yaml
Topic: user-output  
Key: 用户地址
Value: 交易数据 (JSON)
```

## 6. 统计数据流

### 6.1 实时统计收集

#### 6.1.1 统计指标收集
```go
// 统计数据收集点
s.stats.IncrementStat("swap_tx")           // 交换交易计数
s.stats.IncrementStat("db_success")        // 数据库成功写入
s.stats.IncrementStat("parse_error")       // 解析错误
s.stats.IncrementStat("address_filter")    // 地址过滤
s.stats.IncrementStat("token_forward")     // 代币转发
```

#### 6.1.2 时间窗口统计
```go
// 30秒滚动窗口统计
func (s *StatsService) StartCollector(ctx context.Context, interval time.Duration, tokenService *TokenService) {
    ticker := time.NewTicker(interval)
    go func() {
        for {
            select {
            case <-ticker.C:
                s.RotateWindow()      // 轮换统计窗口
                s.PrintStats(tokenService) // 打印统计信息
            case <-ctx.Done():
                return
            }
        }
    }()
}
```

### 6.2 监控数据输出

#### 6.2.1 日志输出格式
```
[Performance] TPS: 1250.5, Avg: 0.8ms, P95: 2.1ms, P99: 5.3ms
[Database] Success: 37515, Errors: 23, Success Rate: 99.94%
[Parser] Swap: 37515, Parse_Err: 45, Process_Err: 12
[Filter] Address: 12450, Token_Fwd: 8920
[Kafka] User_OK: 37515, User_Err: 0, Fwd_OK: 8920, Fwd_Err: 0
[Token] 当前监控 3 个token:
  - Token: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263, Target: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263, Count: 2
```

## 7. 错误处理数据流

### 7.1 错误分类和路由

#### 7.1.1 解析错误处理
```go
if err != nil {
    s.stats.IncrementStat("parse_error")
    log.Printf("Parse error for tx %s: %v", txSigStr, err)
    return nil // 跳过错误交易，继续处理
}
```

#### 7.1.2 数据库错误处理
```go
if dbError := database.InsertMemeTx(tx, network, tokenAddr); dbError != nil {
    s.stats.IncrementStat("db_error")
    log.Printf("Database insert failed: %v", dbError)
    // 可选：将失败的数据写入死信队列
}
```

### 7.2 容错和恢复机制

#### 7.2.1 连接重试
```go
// Kafka 连接重试
for {
    if ctx.Err() != nil {
        return
    }
    readCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
    m, err := r.ReadMessage(readCtx)
    cancel()
    if err != nil {
        if readCtx.Err() == context.DeadlineExceeded {
            continue // 超时重试
        }
        log.Printf("Error reading message: %v", err)
        continue
    }
    // 处理消息
}
```

#### 7.2.2 优雅降级
```go
// 数据库连接失败时的降级处理
if database == nil {
    log.Println("Database unavailable, skipping storage")
    s.stats.IncrementStat("db_unavailable")
    // 继续其他处理流程（如 WebSocket 推送）
}
```
