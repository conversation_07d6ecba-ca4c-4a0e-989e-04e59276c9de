package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"model-processor/config"
	"model-processor/kafka"
	"model-processor/model"
	"model-processor/service"
	"os"
	"os/signal"
	"runtime/debug"
	"sync"
	"syscall"
	"time"
	// "github.com/rs/zerolog/log" // Example if using zerolog
)

// App holds application dependencies and state.
type App struct {
	config         *config.Config
	kClient        *kafka.Client
	database       model.TimeSeriesDB
	txService      *service.TransactionService
	addressService *service.AddressService
	tokenService   *service.TokenService
	statsService   *service.StatsService
}

// NewApp creates and initializes a new application instance.
func NewApp(cfg *config.Config) (*App, error) {
	// Initialize database based on configuration
	var dbConfig interface{}
	var dbType model.DatabaseType

	switch cfg.Database.Type {
	case "tdengine":
		dbType = model.DatabaseTypeTDengine
		dbConfig = cfg.TDengine
	case "influxdb":
		dbType = model.DatabaseTypeInfluxDB
		dbConfig = cfg.InfluxDB
	default:
		return nil, fmt.Errorf("unsupported database type: %s", cfg.Database.Type)
	}

	// 使用新的数据库管理器API
	if err := model.InitDatabaseManager(dbType, dbConfig); err != nil {
		return nil, fmt.Errorf("failed to initialize %s database: %w", cfg.Database.Type, err)
	}

	// 获取数据库管理器和数据库实例
	dbManager := model.GetDatabaseManager()
	if dbManager == nil {
		return nil, fmt.Errorf("database manager not initialized")
	}

	database := dbManager.GetCurrentDatabase()
	if database == nil {
		return nil, fmt.Errorf("database instance not available")
	}

	log.Printf("%s database connection initialized.", database.GetType())
	// Initialize Kafka Client
	kClient, err := kafka.NewClient(cfg.Kafka)
	if err != nil {
		model.CloseDatabaseManager()
		return nil, fmt.Errorf("failed to initialize Kafka client: %w", err)
	}
	log.Println("Kafka client initialized.")

	// Initialize Services
	statsService := service.NewStatsService()
	txService := service.NewTransactionService(kClient, statsService, database)
	addressService := service.NewAddressService()
	tokenService := service.NewTokenService(kClient, cfg.Kafka.TokenOutputTopic)

	// Connect the services so transaction service can check addresses and tokens
	txService.SetAddressService(addressService)
	txService.SetTokenService(tokenService)
	log.Println("Services initialized.")

	// Create and return App instance
	app := &App{
		config:         cfg,
		kClient:        kClient,
		database:       database,
		txService:      txService,
		addressService: addressService,
		tokenService:   tokenService,
		statsService:   statsService,
	}
	return app, nil
}

// Close gracefully shuts down application resources.
func (a *App) Close() {
	log.Println("Shutting down application resources...")
	if a.kClient != nil {
		a.kClient.Close()
	}
	if a.database != nil {
		// 使用新的数据库管理器API进行清理
		if err := model.CloseDatabaseManager(); err != nil {
			log.Printf("Error closing database manager: %v", err)
		}
	}
	log.Println("Application resources closed.")
}

// Start runs the main application logic.
func (a *App) Start() {
	log.Println("Starting application...")

	// Create context and waitgroup for managing goroutines
	ctx, cancel := context.WithCancel(context.Background())
	var wg sync.WaitGroup

	// 启动统计收集器，每30秒统计一次最近30秒内的处理情况
	a.statsService.StartCollector(ctx, 30*time.Second, a.tokenService)
	log.Println("Statistics collector started with 30 second interval.")

	// Create message handler that routes messages to appropriate service
	txMessageHandler := func(messageValue []byte, partitionID int32) error {
		return a.txService.ProcessUpdate(messageValue, partitionID)
	}

	// 创建地址更新处理器
	addrMessageHandler := func(messageKey []byte, messageValue []byte) error {
		return a.addressService.ProcessUpdate(messageKey, messageValue)
	}

	// 创建代币监控处理器
	tokenMessageHandler := func(messageKey []byte, messageValue []byte) error {
		return a.tokenService.ProcessUpdate(messageKey, messageValue)
	}

	// Start Kafka consumer, passing all handlers
	if err := a.kClient.StartConsumer(ctx, &wg, a.config.Kafka, txMessageHandler, addrMessageHandler, tokenMessageHandler); err != nil {
		log.Printf("ERROR starting Kafka consumer: %v. Shutting down.", err)
		cancel() // Cancel context if consumer fails to start
	} else {
		log.Println("Kafka consumer started.")
	}

	// Start a goroutine to print message counts periodically
	wg.Add(1)
	go func() {
		defer wg.Done()
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				inputCount, addrCount := a.kClient.GetMessageCounts()
				stats := a.statsService.GetPreviousWindowStats()
				log.Printf("Message counts - Input: %d, Address: %d", inputCount, addrCount)
				log.Printf("Transaction stats - Swap TX: %d, DB Success: %d, Parse Errors: %d, DB Errors: %d, Address Filtered: %d, Token Forwarded: %d",
					stats["swap_tx_count"],
					stats["db_success_count"],
					stats["parse_error_count"],
					stats["db_error_count"],
					stats["address_filter_count"],
					stats["token_forward_count"])
			case <-ctx.Done():
				return
			}
		}
	}()

	// --- Signal Handling ---
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, syscall.SIGINT, syscall.SIGTERM)

	// Block until a signal is received
	sig := <-signals
	log.Printf("Received signal (%s), initiating shutdown...", sig)

	// --- Graceful Shutdown ---
	// Signal goroutines to stop
	cancel()

	// Wait for goroutines to finish with timeout
	log.Println("Waiting for goroutines to complete...")
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Println("All goroutines finished.")
	case <-time.After(10 * time.Second):
		log.Println("Timeout waiting for goroutines to finish, forcing shutdown...")
	}

	// Print final message counts
	inputCount, addrCount := a.kClient.GetMessageCounts()
	stats := a.statsService.GetPreviousWindowStats()
	log.Printf("Final message counts - Input: %d, Address: %d\n"+
		"Transaction stats:\n"+
		"  - Swap transactions: %d\n"+
		"  - DB writes: %d\n"+
		"  - Parse errors: %d\n"+
		"  - Process errors: %d\n"+
		"  - DB errors: %d\n"+
		"  - Address filtered: %d\n"+
		"  - Token forwarded: %d",
		inputCount,
		addrCount,
		stats["swap_tx_count"],
		stats["db_success_count"],
		stats["parse_error_count"],
		stats["process_error_count"],
		stats["db_error_count"],
		stats["address_filter_count"],
		stats["token_forward_count"])
}

/*
Example Transactions:
- Orca: 2kAW5GAhPZjM3NoSrhJVHdEpwjmq9neWtckWnjopCfsmCGB27e3v2ZyMM79FdsL4VWGEtYSFi1sF1Zhs7bqdoaVT
- Pumpfun: 4Cod1cNGv6RboJ7rSB79yeVCR4Lfd25rFgLY3eiPJfTJjTGyYP1r2i1upAYZHQsWDqUbGd1bhTRm1bpSQcpWMnEz
- Banana Gun: oXUd22GQ1d45a6XNzfdpHAX6NfFEfFa9o2Awn2oimY89Rms3PmXL1uBJx3CnTYjULJw6uim174b3PLBFkaAxKzK
- Jupiter: DBctXdTTtvn7Rr4ikeJFCBz4AtHmJRyjHGQFpE59LuY3Shb7UcRJThAXC7TGRXXskXuu9LEm9RqtU6mWxe5cjPF
- Jupiter DCA: 4mxr44yo5Qi7Rabwbknkh8MNUEWAMKmzFQEmqUVdx5JpHEEuh59TrqiMCjZ7mgZMozRK1zW8me34w8Myi8Qi1tWP
- Meteora DLMM: 125MRda3h1pwGZpPRwSRdesTPiETaKvy4gdiizyc3SWAik4cECqKGw2gggwyA1sb2uekQVkupA2X9S4vKjbstxx3
- Rayd V4: 5kaAWK5X9DdMmsWm6skaUXLd6prFisuYJavd9B62A941nRGcrmwvncg3tRtUfn7TcMLsrrmjCChdEjK3sjxS6YG9
- Rayd Routing: 51nj5GtAmDC23QkeyfCNfTJ6Pdgwx7eq4BARfq1sMmeEaPeLsx9stFA3Dzt9MeLV5xFujBgvghLGcayC3ZevaQYi
- Rayd CPMM: afUCiFQ6amxuxx2AAwsghLt7Q9GYqHfZiF4u3AHhAzs8p1ThzmrtSUFMbcdJy8UnQNTa35Fb1YqxR6F9JMZynYp
- Rayd Concentrated Liquidity SwapV2: 2durZHGFkK4vjpWFGc5GWh5miDs8ke8nWkuee8AUYJA8F9qqT2Um76Q5jGsbK3w2MMgqwZKbnENTLWZoi3d6o2Ds
- Rayd Concentrated Liquidity Swap: 4MSVpVBwxnYTQSF3bSrAB99a3pVr6P6bgoCRDsrBbDMA77WeQqoBDDDXqEh8WpnUy5U4GeotdCG9xyExjNTjYE1u
- Maestro: mWaH4FELcPj4zeY4Cgk5gxUirQDM7yE54VgMEVaqiUDQjStyzwNrxLx4FMEaKEHQoYsgCRhc1YdmBvhGDRVgRrq
- Meteora Pools Program: 4uuw76SPksFw6PvxLFkG9jRyReV1F4EyPYNc3DdSECip8tM22ewqGWJUaRZ1SJEZpuLJz1qPTEPb2es8Zuegng9Z
- Moonshot: AhiFQX1Z3VYbkKQH64ryPDRwxUv8oEPzQVjSvT7zY58UYDm4Yvkkt2Ee9VtSXtF6fJz8fXmb5j3xYVDF17Gr9CG (Buy)
- Moonshot: 2XYu86VrUXiwNNj8WvngcXGytrCsSrpay69Rt3XBz9YZvCQcZJLjvDfh9UWETFtFW47vi4xG2CkiarRJwSe6VekE (Sell)
- Multiple AMMs: 46Jp5EEUrmdCVcE3jeewqUmsMHhqiWWtj243UZNDFZ3mmma6h2DF4AkgPE9ToRYVLVrfKQCJphrvxbNk68Lub9vw //! not supported yet
- OKX: 5xaT2SXQUyvyLGsnyyoKMwsDoHrx1enCKofkdRMdNaL5MW26gjQBM3AWebwjTJ49uqEqnFu5d9nXJek6gUSGCqbL
*/

func main() {
	// Configure logging to show file and line number
	log.SetFlags(log.Ldate | log.Ltime | log.Lshortfile)
	// 保留简单的启动标记
	log.Println("MODEL PROCESSOR STARTING")

	// Catch panics
	defer func() {
		if r := recover(); r != nil {
			log.Printf("PANIC RECOVERED: %v\n%s", r, debug.Stack())
			os.Exit(1)
		}
	}()

	// Optional: Add a flag for specifying config file path
	configPath := flag.String("config", "", "Path to config file (e.g., config.yaml)")
	flag.Parse()

	// Load Configuration
	cfg, err := config.LoadConfig(*configPath)
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}
	log.Println("Configuration loaded successfully.")

	app, err := NewApp(cfg)
	if err != nil {
		log.Fatalf("Failed to initialize application: %v", err)
	}
	defer app.Close()

	// 简化启动日志
	log.Println("APPLICATION INITIALIZED - STARTING NOW")

	// Run the application
	app.Start()
}
