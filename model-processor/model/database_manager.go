package model

import (
	"fmt"
	"log"
)

// DatabaseManager 数据库管理器
type DatabaseManager struct {
	currentDB TimeSeriesDB
}

var dbManager *DatabaseManager

// InitDatabaseManager 初始化数据库管理器
func InitDatabaseManager(dbType DatabaseType, config interface{}) error {
	var db TimeSeriesDB

	switch dbType {
	case DatabaseTypeTDengine:
		db = &TDengineDB{}
	case DatabaseTypeInfluxDB:
		db = &InfluxDBSimple{}
	default:
		return fmt.Errorf("unsupported database type: %s", dbType)
	}

	err := db.Init(config)
	if err != nil {
		return fmt.Errorf("failed to initialize %s database: %v", dbType, err)
	}

	dbManager = &DatabaseManager{
		currentDB: db,
	}

	log.Printf("Database manager initialized with %s", db.GetType())
	return nil
}

// GetDatabaseManager 获取数据库管理器
func GetDatabaseManager() *DatabaseManager {
	return dbManager
}

// GetCurrentDatabase 获取当前数据库实例
func (dm *DatabaseManager) GetCurrentDatabase() TimeSeriesDB {
	return dm.currentDB
}

// Close 关闭数据库管理器
func (dm *DatabaseManager) Close() error {
	if dm.currentDB != nil {
		return dm.currentDB.Close()
	}
	return nil
}

// CloseDatabaseManager 关闭数据库管理器
func CloseDatabaseManager() error {
	if dbManager != nil {
		return dbManager.Close()
	}
	return nil
}

// 为了向后兼容，修改数据库接口的实现
func InitDatabase(dbType DatabaseType, config interface{}) error {
	return InitDatabaseManager(dbType, config)
}

func GetDatabase() TimeSeriesDB {
	if dbManager == nil {
		return nil
	}
	return dbManager.GetCurrentDatabase()
}

func CloseDatabase() error {
	return CloseDatabaseManager()
}
