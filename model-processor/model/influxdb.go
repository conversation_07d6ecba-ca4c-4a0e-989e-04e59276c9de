package model

import (
	"context"
	"fmt"
	"log"
	"model-processor/config"
	"strconv"
	"sync"
	"time"

	influxdb2 "github.com/influxdata/influxdb-client-go/v2"
	"github.com/influxdata/influxdb-client-go/v2/api"
	"github.com/influxdata/influxdb-client-go/v2/api/write"
)

// InfluxDBBatchBuffer InfluxDB专用批量缓冲区
type InfluxDBBatchBuffer struct {
	points    []*write.Point
	mutex     sync.Mutex
	lastFlush time.Time
}

// InfluxDBSimple 简化的InfluxDB实现
type InfluxDBSimple struct {
	config        config.InfluxDBConfig
	client        influxdb2.Client
	writeAPI      api.WriteAPI
	queryAPI      api.QueryAPI
	ctx           context.Context
	batchBuffers  map[int]*InfluxDBBatchBuffer
	bufferMutex   sync.RWMutex
	batchSize     int
	flushInterval time.Duration
}

// Init 初始化InfluxDB连接
func (idb *InfluxDBSimple) Init(cfg interface{}) error {
	dbConfig, ok := cfg.(config.InfluxDBConfig)
	if !ok {
		return fmt.Errorf("invalid config type for InfluxDB")
	}

	idb.config = dbConfig
	idb.ctx = context.Background()
	idb.batchBuffers = make(map[int]*InfluxDBBatchBuffer)
	idb.batchSize = 1000
	idb.flushInterval = 5 * time.Second

	// 使用token认证（推荐）或者用户名密码认证
	if dbConfig.Token != "" {
		idb.client = influxdb2.NewClient(dbConfig.Host, dbConfig.Token)
	} else {
		idb.client = influxdb2.NewClientWithOptions(dbConfig.Host, "",
			influxdb2.DefaultOptions().SetUseGZip(true).SetBatchSize(uint(idb.batchSize)))
	}

	// 获取写入API
	idb.writeAPI = idb.client.WriteAPI(dbConfig.Organization, dbConfig.Bucket)

	// 获取查询API
	idb.queryAPI = idb.client.QueryAPI(dbConfig.Organization)

	log.Printf("InfluxDB client initialized for %s", dbConfig.Host)
	return nil
}

// Close 关闭InfluxDB连接
func (idb *InfluxDBSimple) Close() error {
	if idb.client != nil {
		idb.client.Close()
		log.Println("InfluxDB connection closed.")
	}
	return nil
}

// GetType 返回数据库类型
func (idb *InfluxDBSimple) GetType() string {
	return "influxdb"
}

// HealthCheck 健康检查
func (idb *InfluxDBSimple) HealthCheck() error {
	if idb.client == nil {
		return fmt.Errorf("InfluxDB client is not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	health, err := idb.client.Health(ctx)
	if err != nil {
		return err
	}

	if health.Status != "pass" {
		return fmt.Errorf("InfluxDB health check failed: %s", health.Status)
	}

	return nil
}

// InsertMemeTx 插入单条记录
func (idb *InfluxDBSimple) InsertMemeTx(tx MemeTx, network string, tokenAddr string) error {
	return idb.insertMemeTx(tx, network, tokenAddr)
}

// InsertMemeTxWithThreadID 使用线程专用连接插入记录
func (idb *InfluxDBSimple) InsertMemeTxWithThreadID(tx MemeTx, network string, tokenAddr string, threadID int) error {
	return idb.insertMemeTx(tx, network, tokenAddr)
}

// InsertMemeTxBatch 批量插入记录（使用InfluxDB的批量写入）
func (idb *InfluxDBSimple) InsertMemeTxBatch(tx MemeTx, network string, tokenAddr string, threadID int) error {
	buffer := idb.getBatchBuffer(threadID)

	buffer.mutex.Lock()
	defer buffer.mutex.Unlock()

	// 创建交易point
	txPoint := idb.createTxPoint(tx, network, tokenAddr)
	buffer.points = append(buffer.points, txPoint)

	// 检查是否需要刷新
	if len(buffer.points) >= idb.batchSize {
		return idb.flushBatchBuffer(buffer, threadID)
	}

	return nil
}

// getBatchBuffer 获取或创建线程专用的批量缓冲区
func (idb *InfluxDBSimple) getBatchBuffer(threadID int) *InfluxDBBatchBuffer {
	idb.bufferMutex.RLock()
	buffer, exists := idb.batchBuffers[threadID]
	idb.bufferMutex.RUnlock()

	if exists {
		return buffer
	}

	idb.bufferMutex.Lock()
	defer idb.bufferMutex.Unlock()

	// 双重检查
	buffer, exists = idb.batchBuffers[threadID]
	if exists {
		return buffer
	}

	buffer = &InfluxDBBatchBuffer{
		points:    make([]*write.Point, 0, idb.batchSize),
		lastFlush: time.Now(),
	}
	idb.batchBuffers[threadID] = buffer

	// 启动定时刷新goroutine
	go func() {
		ticker := time.NewTicker(idb.flushInterval)
		defer ticker.Stop()

		for range ticker.C {
			buffer.mutex.Lock()
			if time.Since(buffer.lastFlush) >= idb.flushInterval && len(buffer.points) > 0 {
				idb.flushBatchBuffer(buffer, threadID)
			}
			buffer.mutex.Unlock()
		}
	}()

	return buffer
}

// flushBatchBuffer 刷新批量缓冲区
func (idb *InfluxDBSimple) flushBatchBuffer(buffer *InfluxDBBatchBuffer, threadID int) error {
	if len(buffer.points) == 0 {
		return nil
	}
	for _, pt := range buffer.points {
		idb.writeAPI.WritePoint(pt)
	}
	log.Printf("InfluxDB Thread %d: async wrote %d points", threadID, len(buffer.points))
	buffer.points = buffer.points[:0]
	buffer.lastFlush = time.Now()
	return nil
}

func (idb *InfluxDBSimple) createTxPoint(tx MemeTx, network string, tokenAddr string) *write.Point {
	// 解析数值字段
	amountIn, _ := strconv.ParseInt(tx.AmountIn, 10, 64)
	amountOut, _ := strconv.ParseInt(tx.AmountOut, 10, 64)

	point := write.NewPoint("solana_transaction",
		map[string]string{
			"token_addr":        tokenAddr,
			"tx_type":           tx.TxType,
			"user_addr":         tx.UserAddr,
			"instruction_index": strconv.FormatInt(int64(tx.InstructionIndex), 10),
		},
		map[string]interface{}{
			"network":        network,
			"token_in_addr":  tx.TokenInAddr,
			"token_out_addr": tx.TokenOutAddr,
			"tx_hash":        tx.TxHash,
			"block_time":     tx.BlockTime,
			"amount_in":      amountIn,
			"amount_out":     amountOut,
			"block_slot":     tx.BlockSlot,
			"tx_index":       tx.TxIndex,
		},
		tx.Ts,
	)

	return point
}

// insertMemeTx 内部插入方法（单条记录）
func (idb *InfluxDBSimple) insertMemeTx(tx MemeTx, network string, tokenAddr string) error {
	// 创建代币交易point
	tokenPoint := idb.createTxPoint(tx, network, tokenAddr)
	idb.writeAPI.WritePoint(tokenPoint)
	// 异步写入，无需等待和处理 error
	return nil
}

// QueryMemeTxByHash 根据交易哈希查询
func (idb *InfluxDBSimple) QueryMemeTxByHash(txHash string) ([]MemeTx, error) {
	query := fmt.Sprintf(`
		from(bucket: "%s")
			|> range(start: -30d)
			|> filter(fn: (r) => r._measurement == "solana_transaction_user" or r._measurement == "solana_transaction_token")
			|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
			|> filter(fn: (r) => r.tx_hash == "%s")
			|> limit(n: 10)
	`, idb.config.Bucket, txHash)

	return idb.executeQuery(query)
}

// QueryMemeTxByUser 根据用户地址查询
func (idb *InfluxDBSimple) QueryMemeTxByUser(userAddr string, limit int) ([]MemeTx, error) {
	if limit <= 0 {
		limit = 100
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
			|> range(start: -30d)
			|> filter(fn: (r) => r._measurement == "solana_transaction_user")
			|> filter(fn: (r) => r.user_addr == "%s")
			|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
			|> sort(columns: ["_time"], desc: true)
			|> limit(n: %d)
	`, idb.config.Bucket, userAddr, limit)

	return idb.executeQuery(query)
}

// QueryMemeTxByToken 根据代币地址查询
func (idb *InfluxDBSimple) QueryMemeTxByToken(tokenAddr string, limit int) ([]MemeTx, error) {
	if limit <= 0 {
		limit = 100
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
			|> range(start: -30d)
			|> filter(fn: (r) => r._measurement == "solana_transaction_token")
			|> filter(fn: (r) => r.token_addr == "%s")
			|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
			|> sort(columns: ["_time"], desc: true)
			|> limit(n: %d)
	`, idb.config.Bucket, tokenAddr, limit)

	return idb.executeQuery(query)
}

// QueryMemeTxByTimeRange 根据时间范围查询
func (idb *InfluxDBSimple) QueryMemeTxByTimeRange(startTime, endTime time.Time, limit int) ([]MemeTx, error) {
	if limit <= 0 {
		limit = 100
	}

	query := fmt.Sprintf(`
		from(bucket: "%s")
			|> range(start: %s, stop: %s)
			|> filter(fn: (r) => r._measurement == "solana_transaction_user" or r._measurement == "solana_transaction_token")
			|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
			|> sort(columns: ["_time"], desc: true)
			|> limit(n: %d)
	`, idb.config.Bucket, startTime.Format(time.RFC3339), endTime.Format(time.RFC3339), limit)

	return idb.executeQuery(query)
}

// DeleteMemeTxByHash 根据交易哈希删除
func (idb *InfluxDBSimple) DeleteMemeTxByHash(txHash string) (int64, error) {
	// InfluxDB删除操作使用delete predicate
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 构建删除时间范围 - 删除过去30天内的数据
	stop := time.Now()
	start := stop.Add(-30 * 24 * time.Hour)

	// 删除用户表中的记录
	userDeletePredicate := fmt.Sprintf(`_measurement="solana_transaction_user" AND tx_hash="%s"`, txHash)
	err := idb.client.DeleteAPI().DeleteWithName(ctx, idb.config.Organization, idb.config.Bucket, start, stop, userDeletePredicate)
	if err != nil {
		log.Printf("InfluxDB: Error deleting user transaction %s: %v", txHash, err)
		return 0, err
	}

	// 删除代币表中的记录
	tokenDeletePredicate := fmt.Sprintf(`_measurement="solana_transaction_token" AND tx_hash="%s"`, txHash)
	err = idb.client.DeleteAPI().DeleteWithName(ctx, idb.config.Organization, idb.config.Bucket, start, stop, tokenDeletePredicate)
	if err != nil {
		log.Printf("InfluxDB: Error deleting token transaction %s: %v", txHash, err)
		return 0, err
	}

	log.Printf("InfluxDB: Deleted transactions with hash %s", txHash)
	// InfluxDB的删除操作不返回删除的具体行数，返回固定值表示操作成功
	return 2, nil // 删除了用户表和代币表各一条记录
}

// executeQuery 执行查询并解析结果
func (idb *InfluxDBSimple) executeQuery(query string) ([]MemeTx, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := idb.queryAPI.Query(ctx, query)
	if err != nil {
		log.Printf("InfluxDB: Query execution failed: %v", err)
		return nil, err
	}

	var transactions []MemeTx

	for result.Next() {
		record := result.Record()

		// 从查询结果中构建MemeTx结构
		tx := MemeTx{
			Ts: record.Time(),
		}

		// 解析字段值 - 使用InfluxDB记录的正确方法
		if val := record.ValueByKey("tx_hash"); val != nil {
			if str, ok := val.(string); ok {
				tx.TxHash = str
			}
		}

		if val := record.ValueByKey("block_time"); val != nil {
			if i64, ok := val.(int64); ok {
				tx.BlockTime = i64
			} else if f64, ok := val.(float64); ok {
				tx.BlockTime = int64(f64)
			}
		}

		if val := record.ValueByKey("amount_in"); val != nil {
			if f64, ok := val.(float64); ok {
				tx.AmountIn = strconv.FormatFloat(f64, 'f', -1, 64)
			} else if str, ok := val.(string); ok {
				tx.AmountIn = str
			}
		}

		if val := record.ValueByKey("amount_out"); val != nil {
			if f64, ok := val.(float64); ok {
				tx.AmountOut = strconv.FormatFloat(f64, 'f', -1, 64)
			} else if str, ok := val.(string); ok {
				tx.AmountOut = str
			}
		}

		if val := record.ValueByKey("block_slot"); val != nil {
			if i64, ok := val.(int64); ok {
				tx.BlockSlot = i64
			} else if f64, ok := val.(float64); ok {
				tx.BlockSlot = int64(f64)
			}
		}

		if val := record.ValueByKey("instruction_index"); val != nil {
			if i32, ok := val.(int32); ok {
				tx.InstructionIndex = i32
			} else if i64, ok := val.(int64); ok {
				tx.InstructionIndex = int32(i64)
			} else if f64, ok := val.(float64); ok {
				tx.InstructionIndex = int32(f64)
			}
		}

		if val := record.ValueByKey("tx_index"); val != nil {
			if i32, ok := val.(int32); ok {
				tx.TxIndex = int64(i32)
			} else if i64, ok := val.(int64); ok {
				tx.TxIndex = i64
			} else if f64, ok := val.(float64); ok {
				tx.TxIndex = int64(f64)
			}
		}

		// 从标签中获取值
		if userAddr := record.ValueByKey("user_addr"); userAddr != nil {
			if str, ok := userAddr.(string); ok {
				tx.UserAddr = str
			}
		}

		if tokenInAddr := record.ValueByKey("token_in_addr"); tokenInAddr != nil {
			if str, ok := tokenInAddr.(string); ok {
				tx.TokenInAddr = str
			}
		}

		if tokenOutAddr := record.ValueByKey("token_out_addr"); tokenOutAddr != nil {
			if str, ok := tokenOutAddr.(string); ok {
				tx.TokenOutAddr = str
			}
		}

		if txType := record.ValueByKey("tx_type"); txType != nil {
			if str, ok := txType.(string); ok {
				tx.TxType = str
			}
		}

		transactions = append(transactions, tx)
	}

	if result.Err() != nil {
		log.Printf("InfluxDB: Query result processing failed: %v", result.Err())
		return nil, result.Err()
	}

	log.Printf("InfluxDB: Query returned %d transactions", len(transactions))
	return transactions, nil
}
