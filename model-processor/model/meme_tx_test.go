package model

import (
	"fmt"
	"testing"
	"time"

	"model-processor/config" // Assuming config package path
)

// TestInsertMemeTx performs an integration test for inserting data into TDengine.
// IMPORTANT: This test requires a running TDengine instance and assumes the
// 'meme_tx' stable table exists. Configure connection details below.
func TestInsertMemeTx(t *testing.T) {
	// --- Configuration ---
	// TODO: Replace with your actual TEST TDengine connection details
	testConfig := config.TDengineConfig{
		Host:     "127.0.0.1", // Replace with your TDengine host
		Port:     "6041",      // Replace with your TDengine port (RESTful API port)
		User:     "root",      // Replace with your TDengine user
		Password: "taosdata",  // Replace with your TDengine password
		Database: "test",      // Replace with your TDengine database name
	}
	// --- End Configuration ---

	// Initialize DB connection
	err := InitDB(testConfig)
	if err != nil {
		t.Fatalf("Failed to initialize database connection for test: %v", err)
	}
	// Ensure DB connection is closed after the test
	t.Cleanup(CloseDB)

	// Prepare test data
	// Use a unique mint address to avoid conflicts if the table already exists
	// (TDengine might handle this, but safer for testing)
	testMintAddr := fmt.Sprintf("testMintAddr_%d", time.Now().UnixNano())
	testNetwork := "testnet"

	testTx := MemeTx{
		Ts:           time.Now(),
		TxHash:       fmt.Sprintf("testHash_%d", time.Now().UnixNano()),
		UserAddr:     "testUserAddr_" + testMintAddr,
		TokenInAddr:  "testTokenIn_" + testMintAddr,
		TokenOutAddr: "testTokenOut_" + testMintAddr,
		AmountIn:     123.45,
		AmountOut:    678.90,
		TxType:       "BUY",
		BlockSlot:    1000,
		TxIndex:      1,
	}

	// Call the function under test
	t.Logf("Attempting to insert into child table: tx_%s", testMintAddr)
	err = InsertMemeTx(testTx, testNetwork, testMintAddr)

	// Assert result
	if err != nil {
		t.Errorf("InsertMemeTx failed: %v", err)
		// Log the query and args again might be helpful in debugging
		// Note: The query string now includes quoted strings directly
		query := fmt.Sprintf(`INSERT INTO tx_%s USING meme_tx TAGS ('%s') VALUES (?, '%s', '%s', '%s', '%s', ?, ?, '%s', ?, ?)`,
			testMintAddr, testNetwork, testTx.TxHash, testTx.UserAddr, testTx.TokenInAddr, testTx.TokenOutAddr, testTx.TxType)
		t.Logf("Failed Query (approximate): %s", query)
		t.Logf("Args passed to Exec: Ts=%v, AmtIn=%v, AmtOut=%v, Slot=%v, Idx=%v",
			testTx.Ts, testTx.AmountIn, testTx.AmountOut, testTx.BlockSlot, testTx.TxIndex)
	} else {
		t.Logf("Successfully inserted test record for tx_hash %s into table tx_%s", testTx.TxHash, testMintAddr)
		// You might want to add a query here to verify the data was actually inserted correctly,
		// but for now, we just check for insertion errors.
	}
}
