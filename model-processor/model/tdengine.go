package model

import (
	"database/sql"
	"fmt"
	"log"
	"model-processor/config"
	"strings"
	"sync"
	"time"

	_ "github.com/taosdata/driver-go/v3/taosRestful"
)

// TDengineBatchBuffer TDengine专用批量缓冲区
type TDengineBatchBuffer struct {
	userTxs   []MemeTxWithMeta
	tokenTxs  []MemeTxWithMeta
	mutex     sync.Mutex
	lastFlush time.Time
}

// TDengineDB TDengine数据库实现
type TDengineDB struct {
	config        config.TDengineConfig
	mainDB        *sql.DB
	connPool      map[int]*sql.DB
	mutex         sync.RWMutex
	batchBuffers  map[int]*TDengineBatchBuffer
	bufferMutex   sync.RWMutex
	batchSize     int
	flushInterval time.Duration
}

// Init 初始化TDengine连接
func (td *TDengineDB) Init(cfg interface{}) error {
	tdConfig, ok := cfg.(config.TDengineConfig)
	if !ok {
		return fmt.Errorf("invalid config type for TDengine")
	}

	td.config = tdConfig
	td.connPool = make(map[int]*sql.DB)
	td.batchBuffers = make(map[int]*TDengineBatchBuffer)
	td.batchSize = 100
	td.flushInterval = 2 * time.Second

	// Connect to TDengine using RESTful API
	dsn := fmt.Sprintf("%s:%s@http(%s:%s)/%s",
		tdConfig.User, tdConfig.Password, tdConfig.Host, tdConfig.Port, tdConfig.Database)

	db, err := sql.Open("taosRestful", dsn)
	if err != nil {
		log.Printf("TDengine: Failed to open connection: %v", err)
		return err
	}

	if err := db.Ping(); err != nil {
		log.Printf("TDengine: Failed to ping database: %v", err)
		db.Close()
		return err
	}

	td.mainDB = db
	log.Printf("Successfully connected to TDengine database '%s' at %s:%s.",
		tdConfig.Database, tdConfig.Host, tdConfig.Port)
	return nil
}

// Close 关闭所有数据库连接
func (td *TDengineDB) Close() error {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	// 关闭主连接
	if td.mainDB != nil {
		if err := td.mainDB.Close(); err != nil {
			log.Printf("Error closing TDengine main connection: %v", err)
		} else {
			log.Println("TDengine main connection closed.")
		}
		td.mainDB = nil
	}

	// 关闭所有线程连接
	for threadID, conn := range td.connPool {
		if err := conn.Close(); err != nil {
			log.Printf("Error closing TDengine thread %d connection: %v", threadID, err)
		} else {
			log.Printf("TDengine thread %d connection closed.", threadID)
		}
		delete(td.connPool, threadID)
	}

	return nil
}

// GetType 返回数据库类型
func (td *TDengineDB) GetType() string {
	return string(DatabaseTypeTDengine)
}

// HealthCheck 检查数据库健康状态
func (td *TDengineDB) HealthCheck() error {
	td.mutex.RLock()
	defer td.mutex.RUnlock()

	if td.mainDB == nil {
		return fmt.Errorf("TDengine connection is not initialized")
	}

	return td.mainDB.Ping()
}

// getThreadConnection 获取或创建线程专用连接
func (td *TDengineDB) getThreadConnection(threadID int) *sql.DB {
	td.mutex.RLock()
	conn, exists := td.connPool[threadID]
	td.mutex.RUnlock()

	if exists && conn != nil {
		return conn
	}

	// 需要创建新连接
	td.mutex.Lock()
	defer td.mutex.Unlock()

	// 双重检查
	conn, exists = td.connPool[threadID]
	if exists && conn != nil {
		return conn
	}

	// 创建新连接
	dsn := fmt.Sprintf("%s:%s@http(%s:%s)/%s",
		td.config.User, td.config.Password, td.config.Host, td.config.Port, td.config.Database)

	var err error
	conn, err = sql.Open("taosRestful", dsn)
	if err != nil {
		log.Printf("TDengine Thread %d: Error creating connection: %v", threadID, err)
		return td.mainDB
	}

	// 设置连接池参数
	conn.SetMaxOpenConns(10)
	conn.SetMaxIdleConns(5)
	conn.SetConnMaxLifetime(5 * time.Minute)

	// 测试连接
	if err = conn.Ping(); err != nil {
		log.Printf("TDengine Thread %d: Error testing connection: %v", threadID, err)
		conn.Close()
		return td.mainDB
	}

	td.connPool[threadID] = conn
	return conn
}

// InsertMemeTx 插入单条记录
func (td *TDengineDB) InsertMemeTx(tx MemeTx, network string, tokenAddr string) error {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return fmt.Errorf("TDengine connection is not initialized")
	}

	return td.insertMemeTxWithConn(tx, network, tokenAddr, conn)
}

// InsertMemeTxWithThreadID 使用线程专用连接插入记录
func (td *TDengineDB) InsertMemeTxWithThreadID(tx MemeTx, network string, tokenAddr string, threadID int) error {
	conn := td.getThreadConnection(threadID)
	if conn == nil {
		return fmt.Errorf("TDengine thread %d: failed to get connection", threadID)
	}

	return td.insertMemeTxWithConn(tx, network, tokenAddr, conn)
}

// InsertMemeTxBatch 批量插入记录（使用自己的缓冲区管理）
func (td *TDengineDB) InsertMemeTxBatch(tx MemeTx, network string, tokenAddr string, threadID int) error {
	buffer := td.getBatchBuffer(threadID)

	buffer.mutex.Lock()
	defer buffer.mutex.Unlock()

	txMeta := MemeTxWithMeta{
		Tx:        tx,
		Network:   network,
		TokenAddr: tokenAddr,
		ThreadID:  threadID,
	}

	buffer.userTxs = append(buffer.userTxs, txMeta)
	buffer.tokenTxs = append(buffer.tokenTxs, txMeta)

	// 检查是否需要刷新
	if len(buffer.userTxs) >= td.batchSize {
		return td.flushBatchBuffer(buffer, threadID)
	}

	return nil
}

// getBatchBuffer 获取或创建线程专用的批量缓冲区
func (td *TDengineDB) getBatchBuffer(threadID int) *TDengineBatchBuffer {
	td.bufferMutex.RLock()
	buffer, exists := td.batchBuffers[threadID]
	td.bufferMutex.RUnlock()

	if exists {
		return buffer
	}

	td.bufferMutex.Lock()
	defer td.bufferMutex.Unlock()

	// 双重检查
	buffer, exists = td.batchBuffers[threadID]
	if exists {
		return buffer
	}

	buffer = &TDengineBatchBuffer{
		userTxs:   make([]MemeTxWithMeta, 0, td.batchSize),
		tokenTxs:  make([]MemeTxWithMeta, 0, td.batchSize),
		lastFlush: time.Now(),
	}
	td.batchBuffers[threadID] = buffer

	// 启动定时刷新goroutine
	go func() {
		ticker := time.NewTicker(td.flushInterval)
		defer ticker.Stop()

		for range ticker.C {
			buffer.mutex.Lock()
			if time.Since(buffer.lastFlush) >= td.flushInterval && (len(buffer.userTxs) > 0 || len(buffer.tokenTxs) > 0) {
				td.flushBatchBuffer(buffer, threadID)
			}
			buffer.mutex.Unlock()
		}
	}()

	return buffer
}

// flushBatchBuffer 刷新批量缓冲区
func (td *TDengineDB) flushBatchBuffer(buffer *TDengineBatchBuffer, threadID int) error {
	if len(buffer.userTxs) == 0 && len(buffer.tokenTxs) == 0 {
		return nil
	}

	conn := td.getThreadConnection(threadID)
	if conn == nil {
		return fmt.Errorf("thread %d: failed to get database connection", threadID)
	}

	// 批量插入用户表
	if len(buffer.userTxs) > 0 {
		if err := td.batchInsertUserTx(buffer.userTxs, conn); err != nil {
			log.Printf("TDengine Thread %d: batch insert user tx failed: %v", threadID, err)
		}
	}

	// 批量插入代币表
	if len(buffer.tokenTxs) > 0 {
		if err := td.batchInsertTokenTx(buffer.tokenTxs, conn); err != nil {
			log.Printf("TDengine Thread %d: batch insert token tx failed: %v", threadID, err)
		}
	}

	// 清空缓冲区
	buffer.userTxs = buffer.userTxs[:0]
	buffer.tokenTxs = buffer.tokenTxs[:0]
	buffer.lastFlush = time.Now()

	return nil
}

// batchInsertUserTx 批量插入用户交易表
func (td *TDengineDB) batchInsertUserTx(txs []MemeTxWithMeta, conn *sql.DB) error {
	if len(txs) == 0 {
		return nil
	}

	// 按用户地址分组
	userGroups := make(map[string][]MemeTxWithMeta)
	for _, txMeta := range txs {
		userAddr := txMeta.Tx.UserAddr
		if userAddr == "" {
			userAddr = "unknown"
		}
		userGroups[userAddr] = append(userGroups[userAddr], txMeta)
	}

	// 为每个用户批量插入
	for userAddr, userTxs := range userGroups {
		if err := td.batchInsertUserGroup(userAddr, userTxs, conn); err != nil {
			return err
		}
	}

	return nil
}

// batchInsertUserGroup 为单个用户批量插入
func (td *TDengineDB) batchInsertUserGroup(userAddr string, txs []MemeTxWithMeta, conn *sql.DB) error {
	if len(txs) == 0 {
		return nil
	}

	safeUserAddrForTableName := strings.Replace(strings.Replace(userAddr, "`", "", -1), "'", "", -1)
	userChildTable := fmt.Sprintf("`user_%s`", safeUserAddrForTableName)
	userSuperTable := "solana_transaction_user"
	safeUserAddr := strings.Replace(userAddr, "'", "", -1)

	// 构建批量插入SQL
	var valueStrings []string

	for _, txMeta := range txs {
		tx := txMeta.Tx
		// 使用毫秒级时间戳，避免超出TDengine范围
		// 如果tx.Ts是零值，使用当前时间
		var tsMilliseconds int64
		if tx.Ts.IsZero() {
			tsMilliseconds = time.Now().UnixMilli()
		} else {
			tsMilliseconds = tx.Ts.UnixMilli()
		}

		// 清理字符串值中的单引号
		safeTxHash := strings.Replace(tx.TxHash, "'", "", -1)
		safeTokenInAddr := strings.Replace(tx.TokenInAddr, "'", "", -1)
		safeTokenOutAddr := strings.Replace(tx.TokenOutAddr, "'", "", -1)
		safeAmountIn := strings.Replace(tx.AmountIn, "'", "", -1)
		safeAmountOut := strings.Replace(tx.AmountOut, "'", "", -1)
		safeTxType := strings.Replace(tx.TxType, "'", "", -1)

		valueString := fmt.Sprintf("(%d, %d, '%s', '%s', '%s', '%s', '%s', '%s', %d, %d, %d)",
			tsMilliseconds,
			tx.BlockTime,
			safeTxHash,
			safeTokenInAddr,
			safeTokenOutAddr,
			safeAmountIn,
			safeAmountOut,
			safeTxType,
			tx.BlockSlot,
			tx.InstructionIndex,
			tx.TxIndex,
		)
		valueStrings = append(valueStrings, valueString)
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return err
	}

	query := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s') VALUES %s`,
		userChildTable, userSuperTable, safeUserAddr, strings.Join(valueStrings, ","))

	_, err = conn.Exec(query)
	if err != nil {
		log.Printf("TDengine: Error batch inserting into user table %s: %v", userChildTable, err)
		return err
	}

	return nil
}

// batchInsertTokenTx 批量插入代币交易表
func (td *TDengineDB) batchInsertTokenTx(txs []MemeTxWithMeta, conn *sql.DB) error {
	if len(txs) == 0 {
		return nil
	}

	// 按代币地址分组
	tokenGroups := make(map[string][]MemeTxWithMeta)
	for _, txMeta := range txs {
		tokenAddr := txMeta.TokenAddr
		if tokenAddr == "" {
			tokenAddr = "unknown"
		}
		tokenGroups[tokenAddr] = append(tokenGroups[tokenAddr], txMeta)
	}

	// 为每个代币批量插入
	for tokenAddr, tokenTxs := range tokenGroups {
		if err := td.batchInsertTokenGroup(tokenAddr, tokenTxs, conn); err != nil {
			return err
		}
	}

	return nil
}

// batchInsertTokenGroup 为单个代币批量插入
func (td *TDengineDB) batchInsertTokenGroup(tokenAddr string, txs []MemeTxWithMeta, conn *sql.DB) error {
	if len(txs) == 0 {
		return nil
	}

	safeTokenAddrForTableName := strings.Replace(strings.Replace(tokenAddr, "`", "", -1), "'", "", -1)
	tokenChildTable := fmt.Sprintf("`token_%s`", safeTokenAddrForTableName)
	tokenSuperTable := "solana_transaction_token"
	safeTokenAddr := strings.Replace(tokenAddr, "'", "", -1)

	// 构建批量插入SQL
	var valueStrings []string

	for _, txMeta := range txs {
		tx := txMeta.Tx
		// 使用毫秒级时间戳，避免超出TDengine范围
		var tsMilliseconds int64
		if tx.Ts.IsZero() {
			tsMilliseconds = time.Now().UnixMilli()
		} else {
			tsMilliseconds = tx.Ts.UnixMilli()
		}

		// 清理字符串值中的单引号
		safeTxHash := strings.Replace(tx.TxHash, "'", "", -1)
		safeUserAddr := strings.Replace(tx.UserAddr, "'", "", -1)
		safeTokenInAddr := strings.Replace(tx.TokenInAddr, "'", "", -1)
		safeTokenOutAddr := strings.Replace(tx.TokenOutAddr, "'", "", -1)
		safeAmountIn := strings.Replace(tx.AmountIn, "'", "", -1)
		safeAmountOut := strings.Replace(tx.AmountOut, "'", "", -1)
		safeTxType := strings.Replace(tx.TxType, "'", "", -1)

		valueString := fmt.Sprintf("(%d, %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, %d, %d)",
			tsMilliseconds,
			tx.BlockTime,
			safeTxHash,
			safeUserAddr,
			safeTokenInAddr,
			safeTokenOutAddr,
			safeAmountIn,
			safeAmountOut,
			safeTxType,
			tx.BlockSlot,
			tx.InstructionIndex,
			tx.TxIndex,
		)
		valueStrings = append(valueStrings, valueString)
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return err
	}

	query := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s') VALUES %s`,
		tokenChildTable, tokenSuperTable, safeTokenAddr, strings.Join(valueStrings, ","))

	_, err = conn.Exec(query)
	if err != nil {
		log.Printf("TDengine: Error batch inserting into token table %s: %v", tokenChildTable, err)
		return err
	}

	return nil
}

// insertMemeTxWithConn 使用指定连接插入MemeTx
func (td *TDengineDB) insertMemeTxWithConn(tx MemeTx, network string, tokenAddr string, conn *sql.DB) error {
	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return err
	}

	// 使用毫秒级时间戳
	var tsMilliseconds int64
	if tx.Ts.IsZero() {
		tsMilliseconds = time.Now().UnixMilli()
	} else {
		tsMilliseconds = tx.Ts.UnixMilli()
	}

	// 清理用户地址，用于表名和标签
	safeUserAddrForTableName := strings.Replace(strings.Replace(tx.UserAddr, "`", "", -1), "'", "", -1)
	userChildTable := fmt.Sprintf("`user_%s`", safeUserAddrForTableName)
	userSuperTable := "solana_transaction_user"

	// 用户表插入 - 使用直接字符串构建避免参数化查询问题
	safeTxHash := strings.Replace(tx.TxHash, "'", "''", -1)
	safeTokenInAddr := strings.Replace(tx.TokenInAddr, "'", "''", -1)
	safeTokenOutAddr := strings.Replace(tx.TokenOutAddr, "'", "''", -1)
	safeAmountIn := strings.Replace(tx.AmountIn, "'", "''", -1)
	safeAmountOut := strings.Replace(tx.AmountOut, "'", "''", -1)
	safeTxType := strings.Replace(tx.TxType, "'", "''", -1)

	userQuery := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s') VALUES (%d, %d, '%s', '%s', '%s', '%s', '%s', '%s', %d, %d, %d)`,
		userChildTable, userSuperTable, strings.Replace(tx.UserAddr, "'", "''", -1),
		tsMilliseconds, tx.BlockTime, safeTxHash, safeTokenInAddr, safeTokenOutAddr,
		safeAmountIn, safeAmountOut, safeTxType, tx.BlockSlot, tx.InstructionIndex, tx.TxIndex)

	_, err = conn.Exec(userQuery)
	if err != nil {
		log.Printf("TDengine: Error inserting into user table %s: %v", userChildTable, err)
		return err
	}

	// 清理代币地址，用于表名和标签
	safeTokenAddrForTableName := strings.Replace(strings.Replace(tokenAddr, "`", "", -1), "'", "", -1)
	tokenChildTable := fmt.Sprintf("`token_%s`", safeTokenAddrForTableName)
	tokenSuperTable := "solana_transaction_token"

	// 代币表插入 - 使用直接字符串构建避免参数化查询问题
	safeUserAddr := strings.Replace(tx.UserAddr, "'", "''", -1)

	tokenQuery := fmt.Sprintf(`INSERT INTO %s USING %s TAGS ('%s') VALUES (%d, %d, '%s', '%s', '%s', '%s', '%s', '%s', '%s', %d, %d, %d)`,
		tokenChildTable, tokenSuperTable, strings.Replace(tokenAddr, "'", "''", -1),
		tsMilliseconds, tx.BlockTime, safeTxHash, safeUserAddr, safeTokenInAddr, safeTokenOutAddr,
		safeAmountIn, safeAmountOut, safeTxType, tx.BlockSlot, tx.InstructionIndex, tx.TxIndex)

	_, err = conn.Exec(tokenQuery)
	if err != nil {
		log.Printf("TDengine: Error inserting into token table %s: %v", tokenChildTable, err)
		return err
	}

	return nil
}

// QueryMemeTxByHash 根据交易哈希查询
func (td *TDengineDB) QueryMemeTxByHash(txHash string) ([]MemeTx, error) {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return nil, fmt.Errorf("TDengine connection is not initialized")
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return nil, err
	}

	// 使用直接字符串构建查询，避免参数化查询问题
	safeTxHash := strings.Replace(txHash, "'", "''", -1)
	query := fmt.Sprintf("SELECT ts, block_time, tx_hash, user_addr, token_in_addr, token_out_addr, amount_in, amount_out, tx_type, block_slot, tx_index FROM solana_transaction_user WHERE tx_hash = '%s'", safeTxHash)

	rows, err := conn.Query(query)
	if err != nil {
		log.Printf("TDengine: Error querying by hash %s: %v", txHash, err)
		return nil, err
	}
	defer rows.Close()

	var results []MemeTx
	for rows.Next() {
		var tx MemeTx
		if err := rows.Scan(
			&tx.Ts, &tx.BlockTime, &tx.TxHash, &tx.UserAddr, &tx.TokenInAddr, &tx.TokenOutAddr,
			&tx.AmountIn, &tx.AmountOut, &tx.TxType, &tx.BlockSlot, &tx.TxIndex,
		); err != nil {
			log.Printf("TDengine: Error scanning row: %v", err)
			continue
		}
		results = append(results, tx)
	}

	return results, nil
}

// QueryMemeTxByUser 根据用户地址查询
func (td *TDengineDB) QueryMemeTxByUser(userAddr string, limit int) ([]MemeTx, error) {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return nil, fmt.Errorf("TDengine connection is not initialized")
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return nil, err
	}

	// 使用直接字符串构建查询，避免参数化查询问题
	safeUserAddr := strings.Replace(userAddr, "'", "''", -1)
	query := fmt.Sprintf("SELECT ts, block_time, tx_hash, user_addr, token_in_addr, token_out_addr, amount_in, amount_out, tx_type, block_slot, tx_index FROM solana_transaction_user WHERE user_addr = '%s' ORDER BY ts DESC LIMIT %d", safeUserAddr, limit)

	rows, err := conn.Query(query)
	if err != nil {
		log.Printf("TDengine: Error querying by user %s: %v", userAddr, err)
		return nil, err
	}
	defer rows.Close()

	var results []MemeTx
	for rows.Next() {
		var tx MemeTx
		if err := rows.Scan(
			&tx.Ts, &tx.BlockTime, &tx.TxHash, &tx.UserAddr, &tx.TokenInAddr, &tx.TokenOutAddr,
			&tx.AmountIn, &tx.AmountOut, &tx.TxType, &tx.BlockSlot, &tx.TxIndex,
		); err != nil {
			log.Printf("TDengine: Error scanning row: %v", err)
			continue
		}
		results = append(results, tx)
	}

	return results, nil
}

// QueryMemeTxByToken 根据代币地址查询
func (td *TDengineDB) QueryMemeTxByToken(tokenAddr string, limit int) ([]MemeTx, error) {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return nil, fmt.Errorf("TDengine connection is not initialized")
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return nil, err
	}

	// 使用直接字符串构建查询，避免参数化查询问题
	safeTokenAddr := strings.Replace(tokenAddr, "'", "''", -1)
	query := fmt.Sprintf("SELECT ts, block_time, tx_hash, user_addr, token_in_addr, token_out_addr, amount_in, amount_out, tx_type, block_slot, tx_index FROM solana_transaction_token WHERE token_addr = '%s' ORDER BY ts DESC LIMIT %d", safeTokenAddr, limit)

	rows, err := conn.Query(query)
	if err != nil {
		log.Printf("TDengine: Error querying by token %s: %v", tokenAddr, err)
		return nil, err
	}
	defer rows.Close()

	var results []MemeTx
	for rows.Next() {
		var tx MemeTx
		if err := rows.Scan(
			&tx.Ts, &tx.BlockTime, &tx.TxHash, &tx.UserAddr, &tx.TokenInAddr, &tx.TokenOutAddr,
			&tx.AmountIn, &tx.AmountOut, &tx.TxType, &tx.BlockSlot, &tx.TxIndex,
		); err != nil {
			log.Printf("TDengine: Error scanning row: %v", err)
			continue
		}
		results = append(results, tx)
	}

	return results, nil
}

// QueryMemeTxByTimeRange 根据时间范围查询
func (td *TDengineDB) QueryMemeTxByTimeRange(startTime, endTime time.Time, limit int) ([]MemeTx, error) {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return nil, fmt.Errorf("TDengine connection is not initialized")
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return nil, err
	}

	// 使用直接字符串构建查询，避免参数化查询问题
	startTimeStr := startTime.Format("2006-01-02 15:04:05.000")
	endTimeStr := endTime.Format("2006-01-02 15:04:05.000")
	query := fmt.Sprintf("SELECT ts, block_time, tx_hash, user_addr, token_in_addr, token_out_addr, amount_in, amount_out, tx_type, block_slot, tx_index FROM solana_transaction_user WHERE ts >= '%s' AND ts <= '%s' ORDER BY ts DESC LIMIT %d", startTimeStr, endTimeStr, limit)

	rows, err := conn.Query(query)
	if err != nil {
		log.Printf("TDengine: Error querying by time range: %v", err)
		return nil, err
	}
	defer rows.Close()

	var results []MemeTx
	for rows.Next() {
		var tx MemeTx
		if err := rows.Scan(
			&tx.Ts, &tx.BlockTime, &tx.TxHash, &tx.UserAddr, &tx.TokenInAddr, &tx.TokenOutAddr,
			&tx.AmountIn, &tx.AmountOut, &tx.TxType, &tx.BlockSlot, &tx.TxIndex,
		); err != nil {
			log.Printf("TDengine: Error scanning row: %v", err)
			continue
		}
		results = append(results, tx)
	}

	return results, nil
}

// DeleteMemeTxByHash 根据交易哈希删除
func (td *TDengineDB) DeleteMemeTxByHash(txHash string) (int64, error) {
	td.mutex.RLock()
	conn := td.mainDB
	td.mutex.RUnlock()

	if conn == nil {
		return 0, fmt.Errorf("TDengine connection is not initialized")
	}

	// 首先选择数据库
	_, err := conn.Exec(fmt.Sprintf("USE %s;", td.config.Database))
	if err != nil {
		log.Printf("TDengine: Error selecting database %s: %v", td.config.Database, err)
		return 0, err
	}

	// 删除用户表中的记录
	userQuery := "DELETE FROM solana_transaction_user WHERE tx_hash = ?"
	userResult, err := conn.Exec(userQuery, txHash)
	if err != nil {
		log.Printf("TDengine: Error deleting from user table: %v", err)
		return 0, err
	}

	userCount, _ := userResult.RowsAffected()

	// 删除代币表中的记录
	tokenQuery := "DELETE FROM solana_transaction_token WHERE tx_hash = ?"
	tokenResult, err := conn.Exec(tokenQuery, txHash)
	if err != nil {
		log.Printf("TDengine: Error deleting from token table: %v", err)
		return userCount, err
	}

	tokenCount, _ := tokenResult.RowsAffected()

	return userCount + tokenCount, nil
}
