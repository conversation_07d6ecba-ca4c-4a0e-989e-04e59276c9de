package model

import (
	"fmt"
	"testing"
	"time"

	"model-processor/config"
)

// TestTDengineDB 测试TDengine数据库操作
func TestTDengineDB(t *testing.T) {
	// 配置测试数据库连接
	testConfig := config.TDengineConfig{
		Host:     "127.0.0.1",
		Port:     "6041",
		User:     "root",
		Password: "taosdata",
		Database: "test",
	}

	// 创建数据库实例
	db := &TDengineDB{}

	// 测试初始化
	t.Run("Init", func(t *testing.T) {
		err := db.Init(testConfig)
		if err != nil {
			t.Skipf("跳过TDengine测试，数据库连接失败: %v", err)
		}

		// 创建测试数据库和表结构
		if db.mainDB != nil {
			// 创建数据库
			_, err = db.mainDB.Exec("CREATE DATABASE IF NOT EXISTS test;")
			if err != nil {
				t.Logf("创建数据库失败（可能已存在）: %v", err)
			}

			// 切换到test数据库
			_, err = db.mainDB.Exec("USE test;")
			if err != nil {
				t.<PERSON>("切换到test数据库失败: %v", err)
			}

			// 创建超级表
			_, err = db.mainDB.Exec(`CREATE STABLE IF NOT EXISTS solana_transaction_user (
				ts TIMESTAMP,
				block_time BIGINT,
				tx_hash VARCHAR(128),
				token_in_addr VARCHAR(128),
				token_out_addr VARCHAR(128),
				amount_in VARCHAR(128),
				amount_out VARCHAR(128),
				tx_type NCHAR(16),
				block_slot BIGINT,
				instruction_index INT,
				tx_index BIGINT
			) TAGS (user_addr VARCHAR(128));`)
			if err != nil {
				t.Logf("创建用户表失败（可能已存在）: %v", err)
			}

			_, err = db.mainDB.Exec(`CREATE STABLE IF NOT EXISTS solana_transaction_token (
				ts TIMESTAMP,
				block_time BIGINT,
				tx_hash VARCHAR(128),
				user_addr VARCHAR(128),
				token_in_addr VARCHAR(128),
				token_out_addr VARCHAR(128),
				amount_in VARCHAR(128),
				amount_out VARCHAR(128),
				tx_type NCHAR(16),
				block_slot BIGINT,
				instruction_index INT,
				tx_index BIGINT
			) TAGS (token_addr VARCHAR(128));`)
			if err != nil {
				t.Logf("创建代币表失败（可能已存在）: %v", err)
			}
		}
	})

	// 确保在测试结束后关闭连接
	defer func() {
		if db.mainDB != nil {
			db.Close()
		}
	}()

	// 如果初始化失败，跳过后续测试
	if db.mainDB == nil {
		t.Skip("TDengine数据库未连接，跳过所有测试")
	}

	// 测试数据库类型
	t.Run("GetType", func(t *testing.T) {
		dbType := db.GetType()
		if dbType != "tdengine" {
			t.Errorf("期望数据库类型为 'tdengine'，实际得到 '%s'", dbType)
		}
	})

	// 测试健康检查
	t.Run("HealthCheck", func(t *testing.T) {
		err := db.HealthCheck()
		if err != nil {
			t.Errorf("健康检查失败: %v", err)
		}
	})

	// 创建测试交易数据
	testTx := createTestMemeTx(t)
	testNetwork := "solana"
	testTokenAddr := fmt.Sprintf("test_token_%d", time.Now().UnixNano())

	// 测试单条插入
	t.Run("InsertMemeTx", func(t *testing.T) {
		err := db.InsertMemeTx(testTx, testNetwork, testTokenAddr)
		if err != nil {
			t.Errorf("单条插入失败: %v", err)
		}
	})

	// 测试线程专用插入
	t.Run("InsertMemeTxWithThreadID", func(t *testing.T) {
		testTx.TxHash = fmt.Sprintf("thread_test_%d", time.Now().UnixNano())
		err := db.InsertMemeTxWithThreadID(testTx, testNetwork, testTokenAddr, 1)
		if err != nil {
			t.Errorf("线程专用插入失败: %v", err)
		}
	})

	// 测试批量插入
	t.Run("InsertMemeTxBatch", func(t *testing.T) {
		batchSize := 150 // 超过缓冲区大小以触发刷新

		for i := 0; i < batchSize; i++ {
			tx := testTx
			tx.TxHash = fmt.Sprintf("batch_test_%d_%d", time.Now().UnixNano(), i)
			tx.TxIndex = int64(i)

			err := db.InsertMemeTxBatch(tx, testNetwork, testTokenAddr, 2)
			if err != nil {
				t.Errorf("批量插入第%d条失败: %v", i, err)
			}
		}

		// 等待批量刷新完成
		time.Sleep(3 * time.Second)
	})

	// 测试查询功能
	t.Run("QueryMemeTxByHash", func(t *testing.T) {
		results, err := db.QueryMemeTxByHash(testTx.TxHash)
		if err != nil {
			t.Errorf("按哈希查询失败: %v", err)
		} else {
			t.Logf("按哈希查询结果: 找到 %d 条记录", len(results))
		}
	})

	t.Run("QueryMemeTxByUser", func(t *testing.T) {
		results, err := db.QueryMemeTxByUser(testTx.UserAddr, 10)
		if err != nil {
			t.Errorf("按用户查询失败: %v", err)
		} else {
			t.Logf("按用户查询结果: 找到 %d 条记录", len(results))
		}
	})

	t.Run("QueryMemeTxByToken", func(t *testing.T) {
		results, err := db.QueryMemeTxByToken(testTokenAddr, 10)
		if err != nil {
			t.Errorf("按代币查询失败: %v", err)
		} else {
			t.Logf("按代币查询结果: 找到 %d 条记录", len(results))
		}
	})

	t.Run("QueryMemeTxByTimeRange", func(t *testing.T) {
		startTime := time.Now().Add(-1 * time.Hour)
		endTime := time.Now().Add(1 * time.Hour)

		results, err := db.QueryMemeTxByTimeRange(startTime, endTime, 10)
		if err != nil {
			t.Errorf("按时间范围查询失败: %v", err)
		} else {
			t.Logf("按时间范围查询结果: 找到 %d 条记录", len(results))
		}
	})
}

// TestTDengineBatchPerformance 测试TDengine批量操作性能
func TestTDengineBatchPerformance(t *testing.T) {
	testConfig := config.TDengineConfig{
		Host:     "127.0.0.1",
		Port:     "6041",
		User:     "root",
		Password: "taosdata",
		Database: "test",
	}

	db := &TDengineDB{}
	err := db.Init(testConfig)
	if err != nil {
		t.Skipf("跳过性能测试，数据库连接失败: %v", err)
	}
	defer db.Close()

	// 创建测试数据库和表结构
	if db.mainDB != nil {
		_, err = db.mainDB.Exec("CREATE DATABASE IF NOT EXISTS test;")
		_, err = db.mainDB.Exec("USE test;")
		_, err = db.mainDB.Exec(`CREATE STABLE IF NOT EXISTS solana_transaction_user (
			ts TIMESTAMP, block_time BIGINT, tx_hash VARCHAR(128), token_in_addr VARCHAR(128),
			token_out_addr VARCHAR(128), amount_in VARCHAR(128), amount_out VARCHAR(128),
			tx_type NCHAR(16), block_slot BIGINT, instruction_index INT, tx_index BIGINT
		) TAGS (user_addr VARCHAR(128));`)
		_, err = db.mainDB.Exec(`CREATE STABLE IF NOT EXISTS solana_transaction_token (
			ts TIMESTAMP, block_time BIGINT, tx_hash VARCHAR(128), user_addr VARCHAR(128),
			token_in_addr VARCHAR(128), token_out_addr VARCHAR(128), amount_in VARCHAR(128),
			amount_out VARCHAR(128), tx_type NCHAR(16), block_slot BIGINT, instruction_index INT,
			tx_index BIGINT
		) TAGS (token_addr VARCHAR(128));`)
	}

	// 性能测试：批量插入1000条记录
	t.Run("BatchPerformance1000", func(t *testing.T) {
		batchCount := 1000
		testNetwork := "solana"
		testTokenAddr := fmt.Sprintf("perf_token_%d", time.Now().UnixNano())

		startTime := time.Now()

		for i := 0; i < batchCount; i++ {
			tx := createTestMemeTx(t)
			tx.TxHash = fmt.Sprintf("perf_test_%d_%d", time.Now().UnixNano(), i)
			tx.TxIndex = int64(i)

			err := db.InsertMemeTxBatch(tx, testNetwork, testTokenAddr, 3)
			if err != nil {
				t.Errorf("性能测试插入第%d条失败: %v", i, err)
			}
		}

		// 等待所有批量操作完成
		time.Sleep(5 * time.Second)

		duration := time.Since(startTime)
		rate := float64(batchCount) / duration.Seconds()

		t.Logf("TDengine批量插入性能测试:")
		t.Logf("- 插入记录数: %d", batchCount)
		t.Logf("- 总耗时: %v", duration)
		t.Logf("- 插入速率: %.2f 记录/秒", rate)

		if rate < 100 {
			t.Errorf("批量插入性能过低: %.2f 记录/秒 < 100 记录/秒", rate)
		}
	})
}

// TestTDengineErrorHandling 测试TDengine错误处理
func TestTDengineErrorHandling(t *testing.T) {
	// 测试无效配置
	t.Run("InvalidConfig", func(t *testing.T) {
		db := &TDengineDB{}
		err := db.Init("invalid config")
		if err == nil {
			t.Error("期望初始化失败，但成功了")
		}
	})

	// 测试无效连接
	t.Run("InvalidConnection", func(t *testing.T) {
		invalidConfig := config.TDengineConfig{
			Host:     "invalid-host",
			Port:     "9999",
			User:     "invalid",
			Password: "invalid",
			Database: "invalid",
		}

		db := &TDengineDB{}
		err := db.Init(invalidConfig)
		if err != nil {
			t.Logf("预期的连接失败: %v", err)
		} else {
			// TDengine RESTful客户端可能不会立即验证连接
			// 这是正常行为，因为RESTful连接是延迟验证的
			t.Logf("TDengine客户端初始化成功（延迟验证）")

			// 尝试执行一个简单的查询来验证连接
			_, err = db.mainDB.Exec("SELECT SERVER_VERSION();")
			if err != nil {
				t.Logf("查询失败，连接无效: %v", err)
			} else {
				t.Logf("查询成功（可能使用了默认连接）")
			}
		}
	})

	// 测试未初始化数据库的操作
	t.Run("UninitializedOperations", func(t *testing.T) {
		db := &TDengineDB{}

		err := db.HealthCheck()
		if err == nil {
			t.Error("期望健康检查失败，但成功了")
		}

		testTx := createTestMemeTx(t)
		err = db.InsertMemeTx(testTx, "solana", "test_token")
		if err == nil {
			t.Error("期望插入失败，但成功了")
		}
	})
}

// createTestMemeTx 创建测试用的MemeTx数据
func createTestMemeTx(t *testing.T) MemeTx {
	now := time.Now()
	return MemeTx{
		Ts:               now,
		BlockTime:        now.Unix(),
		TxHash:           fmt.Sprintf("test_hash_%d", now.UnixNano()),
		UserAddr:         fmt.Sprintf("test_user_%d", now.UnixNano()),
		TokenInAddr:      "So11111111111111111111111111111111111111112", // WSOL
		TokenOutAddr:     fmt.Sprintf("token_out_%d", now.UnixNano()),
		AmountIn:         "1000.5",
		AmountOut:        "999.8",
		TxType:           "BUY",
		BlockSlot:        int64(now.Unix()),
		InstructionIndex: 1,
		TxIndex:          1,
	}
}
