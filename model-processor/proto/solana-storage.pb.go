// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.35.1
// 	protoc        v3.19.6
// source: solana-storage.proto

package proto

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type RewardType int32

const (
	RewardType_Unspecified RewardType = 0
	RewardType_Fee         RewardType = 1
	RewardType_Rent        RewardType = 2
	RewardType_Staking     RewardType = 3
	RewardType_Voting      RewardType = 4
)

// Enum value maps for RewardType.
var (
	RewardType_name = map[int32]string{
		0: "Unspecified",
		1: "Fee",
		2: "Rent",
		3: "Staking",
		4: "Voting",
	}
	RewardType_value = map[string]int32{
		"Unspecified": 0,
		"Fee":         1,
		"Rent":        2,
		"Staking":     3,
		"Voting":      4,
	}
)

func (x RewardType) Enum() *RewardType {
	p := new(RewardType)
	*p = x
	return p
}

func (x RewardType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RewardType) Descriptor() protoreflect.EnumDescriptor {
	return file_solana_storage_proto_enumTypes[0].Descriptor()
}

func (RewardType) Type() protoreflect.EnumType {
	return &file_solana_storage_proto_enumTypes[0]
}

func (x RewardType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RewardType.Descriptor instead.
func (RewardType) EnumDescriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{0}
}

type ConfirmedBlock struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PreviousBlockhash string                  `protobuf:"bytes,1,opt,name=previous_blockhash,json=previousBlockhash,proto3" json:"previous_blockhash,omitempty"`
	Blockhash         string                  `protobuf:"bytes,2,opt,name=blockhash,proto3" json:"blockhash,omitempty"`
	ParentSlot        uint64                  `protobuf:"varint,3,opt,name=parent_slot,json=parentSlot,proto3" json:"parent_slot,omitempty"`
	Transactions      []*ConfirmedTransaction `protobuf:"bytes,4,rep,name=transactions,proto3" json:"transactions,omitempty"`
	Rewards           []*Reward               `protobuf:"bytes,5,rep,name=rewards,proto3" json:"rewards,omitempty"`
	BlockTime         *UnixTimestamp          `protobuf:"bytes,6,opt,name=block_time,json=blockTime,proto3" json:"block_time,omitempty"`
	BlockHeight       *BlockHeight            `protobuf:"bytes,7,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
	NumPartitions     *NumPartitions          `protobuf:"bytes,8,opt,name=num_partitions,json=numPartitions,proto3" json:"num_partitions,omitempty"`
}

func (x *ConfirmedBlock) Reset() {
	*x = ConfirmedBlock{}
	mi := &file_solana_storage_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfirmedBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmedBlock) ProtoMessage() {}

func (x *ConfirmedBlock) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmedBlock.ProtoReflect.Descriptor instead.
func (*ConfirmedBlock) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{0}
}

func (x *ConfirmedBlock) GetPreviousBlockhash() string {
	if x != nil {
		return x.PreviousBlockhash
	}
	return ""
}

func (x *ConfirmedBlock) GetBlockhash() string {
	if x != nil {
		return x.Blockhash
	}
	return ""
}

func (x *ConfirmedBlock) GetParentSlot() uint64 {
	if x != nil {
		return x.ParentSlot
	}
	return 0
}

func (x *ConfirmedBlock) GetTransactions() []*ConfirmedTransaction {
	if x != nil {
		return x.Transactions
	}
	return nil
}

func (x *ConfirmedBlock) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *ConfirmedBlock) GetBlockTime() *UnixTimestamp {
	if x != nil {
		return x.BlockTime
	}
	return nil
}

func (x *ConfirmedBlock) GetBlockHeight() *BlockHeight {
	if x != nil {
		return x.BlockHeight
	}
	return nil
}

func (x *ConfirmedBlock) GetNumPartitions() *NumPartitions {
	if x != nil {
		return x.NumPartitions
	}
	return nil
}

type ConfirmedTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Transaction *Transaction           `protobuf:"bytes,1,opt,name=transaction,proto3" json:"transaction,omitempty"`
	Meta        *TransactionStatusMeta `protobuf:"bytes,2,opt,name=meta,proto3" json:"meta,omitempty"`
}

func (x *ConfirmedTransaction) Reset() {
	*x = ConfirmedTransaction{}
	mi := &file_solana_storage_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfirmedTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfirmedTransaction) ProtoMessage() {}

func (x *ConfirmedTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfirmedTransaction.ProtoReflect.Descriptor instead.
func (*ConfirmedTransaction) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{1}
}

func (x *ConfirmedTransaction) GetTransaction() *Transaction {
	if x != nil {
		return x.Transaction
	}
	return nil
}

func (x *ConfirmedTransaction) GetMeta() *TransactionStatusMeta {
	if x != nil {
		return x.Meta
	}
	return nil
}

type Transaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Signatures [][]byte `protobuf:"bytes,1,rep,name=signatures,proto3" json:"signatures,omitempty"`
	Message    *Message `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *Transaction) Reset() {
	*x = Transaction{}
	mi := &file_solana_storage_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Transaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Transaction) ProtoMessage() {}

func (x *Transaction) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Transaction.ProtoReflect.Descriptor instead.
func (*Transaction) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{2}
}

func (x *Transaction) GetSignatures() [][]byte {
	if x != nil {
		return x.Signatures
	}
	return nil
}

func (x *Transaction) GetMessage() *Message {
	if x != nil {
		return x.Message
	}
	return nil
}

type Message struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Header              *MessageHeader               `protobuf:"bytes,1,opt,name=header,proto3" json:"header,omitempty"`
	AccountKeys         [][]byte                     `protobuf:"bytes,2,rep,name=account_keys,json=accountKeys,proto3" json:"account_keys,omitempty"`
	RecentBlockhash     []byte                       `protobuf:"bytes,3,opt,name=recent_blockhash,json=recentBlockhash,proto3" json:"recent_blockhash,omitempty"`
	Instructions        []*CompiledInstruction       `protobuf:"bytes,4,rep,name=instructions,proto3" json:"instructions,omitempty"`
	Versioned           bool                         `protobuf:"varint,5,opt,name=versioned,proto3" json:"versioned,omitempty"`
	AddressTableLookups []*MessageAddressTableLookup `protobuf:"bytes,6,rep,name=address_table_lookups,json=addressTableLookups,proto3" json:"address_table_lookups,omitempty"`
}

func (x *Message) Reset() {
	*x = Message{}
	mi := &file_solana_storage_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Message) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Message) ProtoMessage() {}

func (x *Message) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Message.ProtoReflect.Descriptor instead.
func (*Message) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{3}
}

func (x *Message) GetHeader() *MessageHeader {
	if x != nil {
		return x.Header
	}
	return nil
}

func (x *Message) GetAccountKeys() [][]byte {
	if x != nil {
		return x.AccountKeys
	}
	return nil
}

func (x *Message) GetRecentBlockhash() []byte {
	if x != nil {
		return x.RecentBlockhash
	}
	return nil
}

func (x *Message) GetInstructions() []*CompiledInstruction {
	if x != nil {
		return x.Instructions
	}
	return nil
}

func (x *Message) GetVersioned() bool {
	if x != nil {
		return x.Versioned
	}
	return false
}

func (x *Message) GetAddressTableLookups() []*MessageAddressTableLookup {
	if x != nil {
		return x.AddressTableLookups
	}
	return nil
}

type MessageHeader struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumRequiredSignatures       uint32 `protobuf:"varint,1,opt,name=num_required_signatures,json=numRequiredSignatures,proto3" json:"num_required_signatures,omitempty"`
	NumReadonlySignedAccounts   uint32 `protobuf:"varint,2,opt,name=num_readonly_signed_accounts,json=numReadonlySignedAccounts,proto3" json:"num_readonly_signed_accounts,omitempty"`
	NumReadonlyUnsignedAccounts uint32 `protobuf:"varint,3,opt,name=num_readonly_unsigned_accounts,json=numReadonlyUnsignedAccounts,proto3" json:"num_readonly_unsigned_accounts,omitempty"`
}

func (x *MessageHeader) Reset() {
	*x = MessageHeader{}
	mi := &file_solana_storage_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageHeader) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageHeader) ProtoMessage() {}

func (x *MessageHeader) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageHeader.ProtoReflect.Descriptor instead.
func (*MessageHeader) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{4}
}

func (x *MessageHeader) GetNumRequiredSignatures() uint32 {
	if x != nil {
		return x.NumRequiredSignatures
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlySignedAccounts() uint32 {
	if x != nil {
		return x.NumReadonlySignedAccounts
	}
	return 0
}

func (x *MessageHeader) GetNumReadonlyUnsignedAccounts() uint32 {
	if x != nil {
		return x.NumReadonlyUnsignedAccounts
	}
	return 0
}

type MessageAddressTableLookup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountKey      []byte `protobuf:"bytes,1,opt,name=account_key,json=accountKey,proto3" json:"account_key,omitempty"`
	WritableIndexes []byte `protobuf:"bytes,2,opt,name=writable_indexes,json=writableIndexes,proto3" json:"writable_indexes,omitempty"`
	ReadonlyIndexes []byte `protobuf:"bytes,3,opt,name=readonly_indexes,json=readonlyIndexes,proto3" json:"readonly_indexes,omitempty"`
}

func (x *MessageAddressTableLookup) Reset() {
	*x = MessageAddressTableLookup{}
	mi := &file_solana_storage_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageAddressTableLookup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageAddressTableLookup) ProtoMessage() {}

func (x *MessageAddressTableLookup) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageAddressTableLookup.ProtoReflect.Descriptor instead.
func (*MessageAddressTableLookup) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{5}
}

func (x *MessageAddressTableLookup) GetAccountKey() []byte {
	if x != nil {
		return x.AccountKey
	}
	return nil
}

func (x *MessageAddressTableLookup) GetWritableIndexes() []byte {
	if x != nil {
		return x.WritableIndexes
	}
	return nil
}

func (x *MessageAddressTableLookup) GetReadonlyIndexes() []byte {
	if x != nil {
		return x.ReadonlyIndexes
	}
	return nil
}

type TransactionStatusMeta struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Err                     *TransactionError    `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
	Fee                     uint64               `protobuf:"varint,2,opt,name=fee,proto3" json:"fee,omitempty"`
	PreBalances             []uint64             `protobuf:"varint,3,rep,packed,name=pre_balances,json=preBalances,proto3" json:"pre_balances,omitempty"`
	PostBalances            []uint64             `protobuf:"varint,4,rep,packed,name=post_balances,json=postBalances,proto3" json:"post_balances,omitempty"`
	InnerInstructions       []*InnerInstructions `protobuf:"bytes,5,rep,name=inner_instructions,json=innerInstructions,proto3" json:"inner_instructions,omitempty"`
	InnerInstructionsNone   bool                 `protobuf:"varint,10,opt,name=inner_instructions_none,json=innerInstructionsNone,proto3" json:"inner_instructions_none,omitempty"`
	LogMessages             []string             `protobuf:"bytes,6,rep,name=log_messages,json=logMessages,proto3" json:"log_messages,omitempty"`
	LogMessagesNone         bool                 `protobuf:"varint,11,opt,name=log_messages_none,json=logMessagesNone,proto3" json:"log_messages_none,omitempty"`
	PreTokenBalances        []*TokenBalance      `protobuf:"bytes,7,rep,name=pre_token_balances,json=preTokenBalances,proto3" json:"pre_token_balances,omitempty"`
	PostTokenBalances       []*TokenBalance      `protobuf:"bytes,8,rep,name=post_token_balances,json=postTokenBalances,proto3" json:"post_token_balances,omitempty"`
	Rewards                 []*Reward            `protobuf:"bytes,9,rep,name=rewards,proto3" json:"rewards,omitempty"`
	LoadedWritableAddresses [][]byte             `protobuf:"bytes,12,rep,name=loaded_writable_addresses,json=loadedWritableAddresses,proto3" json:"loaded_writable_addresses,omitempty"`
	LoadedReadonlyAddresses [][]byte             `protobuf:"bytes,13,rep,name=loaded_readonly_addresses,json=loadedReadonlyAddresses,proto3" json:"loaded_readonly_addresses,omitempty"`
	ReturnData              *ReturnData          `protobuf:"bytes,14,opt,name=return_data,json=returnData,proto3" json:"return_data,omitempty"`
	ReturnDataNone          bool                 `protobuf:"varint,15,opt,name=return_data_none,json=returnDataNone,proto3" json:"return_data_none,omitempty"`
	// Sum of compute units consumed by all instructions.
	// Available since Solana v1.10.35 / v1.11.6.
	// Set to `None` for txs executed on earlier versions.
	ComputeUnitsConsumed *uint64 `protobuf:"varint,16,opt,name=compute_units_consumed,json=computeUnitsConsumed,proto3,oneof" json:"compute_units_consumed,omitempty"`
}

func (x *TransactionStatusMeta) Reset() {
	*x = TransactionStatusMeta{}
	mi := &file_solana_storage_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionStatusMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionStatusMeta) ProtoMessage() {}

func (x *TransactionStatusMeta) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionStatusMeta.ProtoReflect.Descriptor instead.
func (*TransactionStatusMeta) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{6}
}

func (x *TransactionStatusMeta) GetErr() *TransactionError {
	if x != nil {
		return x.Err
	}
	return nil
}

func (x *TransactionStatusMeta) GetFee() uint64 {
	if x != nil {
		return x.Fee
	}
	return 0
}

func (x *TransactionStatusMeta) GetPreBalances() []uint64 {
	if x != nil {
		return x.PreBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetPostBalances() []uint64 {
	if x != nil {
		return x.PostBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetInnerInstructions() []*InnerInstructions {
	if x != nil {
		return x.InnerInstructions
	}
	return nil
}

func (x *TransactionStatusMeta) GetInnerInstructionsNone() bool {
	if x != nil {
		return x.InnerInstructionsNone
	}
	return false
}

func (x *TransactionStatusMeta) GetLogMessages() []string {
	if x != nil {
		return x.LogMessages
	}
	return nil
}

func (x *TransactionStatusMeta) GetLogMessagesNone() bool {
	if x != nil {
		return x.LogMessagesNone
	}
	return false
}

func (x *TransactionStatusMeta) GetPreTokenBalances() []*TokenBalance {
	if x != nil {
		return x.PreTokenBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetPostTokenBalances() []*TokenBalance {
	if x != nil {
		return x.PostTokenBalances
	}
	return nil
}

func (x *TransactionStatusMeta) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *TransactionStatusMeta) GetLoadedWritableAddresses() [][]byte {
	if x != nil {
		return x.LoadedWritableAddresses
	}
	return nil
}

func (x *TransactionStatusMeta) GetLoadedReadonlyAddresses() [][]byte {
	if x != nil {
		return x.LoadedReadonlyAddresses
	}
	return nil
}

func (x *TransactionStatusMeta) GetReturnData() *ReturnData {
	if x != nil {
		return x.ReturnData
	}
	return nil
}

func (x *TransactionStatusMeta) GetReturnDataNone() bool {
	if x != nil {
		return x.ReturnDataNone
	}
	return false
}

func (x *TransactionStatusMeta) GetComputeUnitsConsumed() uint64 {
	if x != nil && x.ComputeUnitsConsumed != nil {
		return *x.ComputeUnitsConsumed
	}
	return 0
}

type TransactionError struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Err []byte `protobuf:"bytes,1,opt,name=err,proto3" json:"err,omitempty"`
}

func (x *TransactionError) Reset() {
	*x = TransactionError{}
	mi := &file_solana_storage_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransactionError) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransactionError) ProtoMessage() {}

func (x *TransactionError) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransactionError.ProtoReflect.Descriptor instead.
func (*TransactionError) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{7}
}

func (x *TransactionError) GetErr() []byte {
	if x != nil {
		return x.Err
	}
	return nil
}

type InnerInstructions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Index        uint32              `protobuf:"varint,1,opt,name=index,proto3" json:"index,omitempty"`
	Instructions []*InnerInstruction `protobuf:"bytes,2,rep,name=instructions,proto3" json:"instructions,omitempty"`
}

func (x *InnerInstructions) Reset() {
	*x = InnerInstructions{}
	mi := &file_solana_storage_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerInstructions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerInstructions) ProtoMessage() {}

func (x *InnerInstructions) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerInstructions.ProtoReflect.Descriptor instead.
func (*InnerInstructions) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{8}
}

func (x *InnerInstructions) GetIndex() uint32 {
	if x != nil {
		return x.Index
	}
	return 0
}

func (x *InnerInstructions) GetInstructions() []*InnerInstruction {
	if x != nil {
		return x.Instructions
	}
	return nil
}

type InnerInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramIdIndex uint32 `protobuf:"varint,1,opt,name=program_id_index,json=programIdIndex,proto3" json:"program_id_index,omitempty"`
	Accounts       []byte `protobuf:"bytes,2,opt,name=accounts,proto3" json:"accounts,omitempty"`
	Data           []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	// Invocation stack height of an inner instruction.
	// Available since Solana v1.14.6
	// Set to `None` for txs executed on earlier versions.
	StackHeight *uint32 `protobuf:"varint,4,opt,name=stack_height,json=stackHeight,proto3,oneof" json:"stack_height,omitempty"`
}

func (x *InnerInstruction) Reset() {
	*x = InnerInstruction{}
	mi := &file_solana_storage_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InnerInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InnerInstruction) ProtoMessage() {}

func (x *InnerInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InnerInstruction.ProtoReflect.Descriptor instead.
func (*InnerInstruction) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{9}
}

func (x *InnerInstruction) GetProgramIdIndex() uint32 {
	if x != nil {
		return x.ProgramIdIndex
	}
	return 0
}

func (x *InnerInstruction) GetAccounts() []byte {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *InnerInstruction) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *InnerInstruction) GetStackHeight() uint32 {
	if x != nil && x.StackHeight != nil {
		return *x.StackHeight
	}
	return 0
}

type CompiledInstruction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramIdIndex uint32 `protobuf:"varint,1,opt,name=program_id_index,json=programIdIndex,proto3" json:"program_id_index,omitempty"`
	Accounts       []byte `protobuf:"bytes,2,opt,name=accounts,proto3" json:"accounts,omitempty"`
	Data           []byte `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *CompiledInstruction) Reset() {
	*x = CompiledInstruction{}
	mi := &file_solana_storage_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CompiledInstruction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompiledInstruction) ProtoMessage() {}

func (x *CompiledInstruction) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompiledInstruction.ProtoReflect.Descriptor instead.
func (*CompiledInstruction) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{10}
}

func (x *CompiledInstruction) GetProgramIdIndex() uint32 {
	if x != nil {
		return x.ProgramIdIndex
	}
	return 0
}

func (x *CompiledInstruction) GetAccounts() []byte {
	if x != nil {
		return x.Accounts
	}
	return nil
}

func (x *CompiledInstruction) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type TokenBalance struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccountIndex  uint32         `protobuf:"varint,1,opt,name=account_index,json=accountIndex,proto3" json:"account_index,omitempty"`
	Mint          string         `protobuf:"bytes,2,opt,name=mint,proto3" json:"mint,omitempty"`
	UiTokenAmount *UiTokenAmount `protobuf:"bytes,3,opt,name=ui_token_amount,json=uiTokenAmount,proto3" json:"ui_token_amount,omitempty"`
	Owner         string         `protobuf:"bytes,4,opt,name=owner,proto3" json:"owner,omitempty"`
	ProgramId     string         `protobuf:"bytes,5,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
}

func (x *TokenBalance) Reset() {
	*x = TokenBalance{}
	mi := &file_solana_storage_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenBalance) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenBalance) ProtoMessage() {}

func (x *TokenBalance) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenBalance.ProtoReflect.Descriptor instead.
func (*TokenBalance) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{11}
}

func (x *TokenBalance) GetAccountIndex() uint32 {
	if x != nil {
		return x.AccountIndex
	}
	return 0
}

func (x *TokenBalance) GetMint() string {
	if x != nil {
		return x.Mint
	}
	return ""
}

func (x *TokenBalance) GetUiTokenAmount() *UiTokenAmount {
	if x != nil {
		return x.UiTokenAmount
	}
	return nil
}

func (x *TokenBalance) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *TokenBalance) GetProgramId() string {
	if x != nil {
		return x.ProgramId
	}
	return ""
}

type UiTokenAmount struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UiAmount       float64 `protobuf:"fixed64,1,opt,name=ui_amount,json=uiAmount,proto3" json:"ui_amount,omitempty"`
	Decimals       uint32  `protobuf:"varint,2,opt,name=decimals,proto3" json:"decimals,omitempty"`
	Amount         string  `protobuf:"bytes,3,opt,name=amount,proto3" json:"amount,omitempty"`
	UiAmountString string  `protobuf:"bytes,4,opt,name=ui_amount_string,json=uiAmountString,proto3" json:"ui_amount_string,omitempty"`
}

func (x *UiTokenAmount) Reset() {
	*x = UiTokenAmount{}
	mi := &file_solana_storage_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UiTokenAmount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UiTokenAmount) ProtoMessage() {}

func (x *UiTokenAmount) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UiTokenAmount.ProtoReflect.Descriptor instead.
func (*UiTokenAmount) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{12}
}

func (x *UiTokenAmount) GetUiAmount() float64 {
	if x != nil {
		return x.UiAmount
	}
	return 0
}

func (x *UiTokenAmount) GetDecimals() uint32 {
	if x != nil {
		return x.Decimals
	}
	return 0
}

func (x *UiTokenAmount) GetAmount() string {
	if x != nil {
		return x.Amount
	}
	return ""
}

func (x *UiTokenAmount) GetUiAmountString() string {
	if x != nil {
		return x.UiAmountString
	}
	return ""
}

type ReturnData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ProgramId []byte `protobuf:"bytes,1,opt,name=program_id,json=programId,proto3" json:"program_id,omitempty"`
	Data      []byte `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *ReturnData) Reset() {
	*x = ReturnData{}
	mi := &file_solana_storage_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReturnData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReturnData) ProtoMessage() {}

func (x *ReturnData) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReturnData.ProtoReflect.Descriptor instead.
func (*ReturnData) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{13}
}

func (x *ReturnData) GetProgramId() []byte {
	if x != nil {
		return x.ProgramId
	}
	return nil
}

func (x *ReturnData) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

type Reward struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pubkey      string     `protobuf:"bytes,1,opt,name=pubkey,proto3" json:"pubkey,omitempty"`
	Lamports    int64      `protobuf:"varint,2,opt,name=lamports,proto3" json:"lamports,omitempty"`
	PostBalance uint64     `protobuf:"varint,3,opt,name=post_balance,json=postBalance,proto3" json:"post_balance,omitempty"`
	RewardType  RewardType `protobuf:"varint,4,opt,name=reward_type,json=rewardType,proto3,enum=solana.storage.ConfirmedBlock.RewardType" json:"reward_type,omitempty"`
	Commission  string     `protobuf:"bytes,5,opt,name=commission,proto3" json:"commission,omitempty"`
}

func (x *Reward) Reset() {
	*x = Reward{}
	mi := &file_solana_storage_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Reward) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Reward) ProtoMessage() {}

func (x *Reward) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Reward.ProtoReflect.Descriptor instead.
func (*Reward) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{14}
}

func (x *Reward) GetPubkey() string {
	if x != nil {
		return x.Pubkey
	}
	return ""
}

func (x *Reward) GetLamports() int64 {
	if x != nil {
		return x.Lamports
	}
	return 0
}

func (x *Reward) GetPostBalance() uint64 {
	if x != nil {
		return x.PostBalance
	}
	return 0
}

func (x *Reward) GetRewardType() RewardType {
	if x != nil {
		return x.RewardType
	}
	return RewardType_Unspecified
}

func (x *Reward) GetCommission() string {
	if x != nil {
		return x.Commission
	}
	return ""
}

type Rewards struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Rewards       []*Reward      `protobuf:"bytes,1,rep,name=rewards,proto3" json:"rewards,omitempty"`
	NumPartitions *NumPartitions `protobuf:"bytes,2,opt,name=num_partitions,json=numPartitions,proto3" json:"num_partitions,omitempty"`
}

func (x *Rewards) Reset() {
	*x = Rewards{}
	mi := &file_solana_storage_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Rewards) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Rewards) ProtoMessage() {}

func (x *Rewards) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Rewards.ProtoReflect.Descriptor instead.
func (*Rewards) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{15}
}

func (x *Rewards) GetRewards() []*Reward {
	if x != nil {
		return x.Rewards
	}
	return nil
}

func (x *Rewards) GetNumPartitions() *NumPartitions {
	if x != nil {
		return x.NumPartitions
	}
	return nil
}

type UnixTimestamp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64 `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
}

func (x *UnixTimestamp) Reset() {
	*x = UnixTimestamp{}
	mi := &file_solana_storage_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UnixTimestamp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UnixTimestamp) ProtoMessage() {}

func (x *UnixTimestamp) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UnixTimestamp.ProtoReflect.Descriptor instead.
func (*UnixTimestamp) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{16}
}

func (x *UnixTimestamp) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type BlockHeight struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BlockHeight uint64 `protobuf:"varint,1,opt,name=block_height,json=blockHeight,proto3" json:"block_height,omitempty"`
}

func (x *BlockHeight) Reset() {
	*x = BlockHeight{}
	mi := &file_solana_storage_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlockHeight) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlockHeight) ProtoMessage() {}

func (x *BlockHeight) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlockHeight.ProtoReflect.Descriptor instead.
func (*BlockHeight) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{17}
}

func (x *BlockHeight) GetBlockHeight() uint64 {
	if x != nil {
		return x.BlockHeight
	}
	return 0
}

type NumPartitions struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NumPartitions uint64 `protobuf:"varint,1,opt,name=num_partitions,json=numPartitions,proto3" json:"num_partitions,omitempty"`
}

func (x *NumPartitions) Reset() {
	*x = NumPartitions{}
	mi := &file_solana_storage_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NumPartitions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NumPartitions) ProtoMessage() {}

func (x *NumPartitions) ProtoReflect() protoreflect.Message {
	mi := &file_solana_storage_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NumPartitions.ProtoReflect.Descriptor instead.
func (*NumPartitions) Descriptor() ([]byte, []int) {
	return file_solana_storage_proto_rawDescGZIP(), []int{18}
}

func (x *NumPartitions) GetNumPartitions() uint64 {
	if x != nil {
		return x.NumPartitions
	}
	return 0
}

var File_solana_storage_proto protoreflect.FileDescriptor

var file_solana_storage_proto_rawDesc = []byte{
	0x0a, 0x14, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x89, 0x04, 0x0a, 0x0e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x2d, 0x0a, 0x12, 0x70, 0x72, 0x65, 0x76,
	0x69, 0x6f, 0x75, 0x73, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x70, 0x72, 0x65, 0x76, 0x69, 0x6f, 0x75, 0x73, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b,
	0x68, 0x61, 0x73, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x68, 0x61, 0x73, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x5f,
	0x73, 0x6c, 0x6f, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x65,
	0x6e, 0x74, 0x53, 0x6c, 0x6f, 0x74, 0x12, 0x57, 0x0a, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73,
	0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0c, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x3f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x25, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73,
	0x12, 0x4b, 0x0a, 0x0a, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x55, 0x6e, 0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x4d, 0x0a,
	0x0c, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x52,
	0x0b, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x53, 0x0a, 0x0e,
	0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x4e, 0x75, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x22, 0xae, 0x01, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0b, 0x74, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x72, 0x61,
	0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x48, 0x0a, 0x04, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e,
	0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65,
	0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x04, 0x6d, 0x65,
	0x74, 0x61, 0x22, 0x6f, 0x0a, 0x0b, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x12, 0x40, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x22, 0x81, 0x03, 0x0a, 0x07, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12,
	0x44, 0x0a, 0x06, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x52, 0x06, 0x68,
	0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x21, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6b, 0x65, 0x79, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x0b, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x63, 0x65,
	0x6e, 0x74, 0x5f, 0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x68, 0x61, 0x73, 0x68, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0c, 0x52, 0x0f, 0x72, 0x65, 0x63, 0x65, 0x6e, 0x74, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x68,
	0x61, 0x73, 0x68, 0x12, 0x56, 0x0a, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x6f, 0x6c, 0x61,
	0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c,
	0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0c, 0x69,
	0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x65, 0x64, 0x12, 0x6c, 0x0a, 0x15, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x6c, 0x6f, 0x6f, 0x6b, 0x75,
	0x70, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e,
	0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72,
	0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c, 0x6f, 0x6f, 0x6b,
	0x75, 0x70, 0x52, 0x13, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65,
	0x4c, 0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x73, 0x22, 0xcd, 0x01, 0x0a, 0x0d, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12, 0x36, 0x0a, 0x17, 0x6e, 0x75, 0x6d,
	0x5f, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x15, 0x6e, 0x75, 0x6d, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65,
	0x73, 0x12, 0x3f, 0x0a, 0x1c, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c,
	0x79, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x19, 0x6e, 0x75, 0x6d, 0x52, 0x65, 0x61, 0x64,
	0x6f, 0x6e, 0x6c, 0x79, 0x53, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x73, 0x12, 0x43, 0x0a, 0x1e, 0x6e, 0x75, 0x6d, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e,
	0x6c, 0x79, 0x5f, 0x75, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x1b, 0x6e, 0x75, 0x6d, 0x52,
	0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x55, 0x6e, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x22, 0x92, 0x01, 0x0a, 0x19, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x4c,
	0x6f, 0x6f, 0x6b, 0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0a, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x29, 0x0a, 0x10, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0f, 0x77, 0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65,
	0x73, 0x12, 0x29, 0x0a, 0x10, 0x72, 0x65, 0x61, 0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x5f, 0x69, 0x6e,
	0x64, 0x65, 0x78, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x0f, 0x72, 0x65, 0x61,
	0x64, 0x6f, 0x6e, 0x6c, 0x79, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x65, 0x73, 0x22, 0xd9, 0x07, 0x0a,
	0x15, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x41, 0x0a, 0x03, 0x65, 0x72, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x03, 0x65, 0x72, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x66, 0x65, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x04, 0x52, 0x03, 0x66, 0x65, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x72, 0x65, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x04, 0x52, 0x0b, 0x70, 0x72, 0x65, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x23,
	0x0a, 0x0d, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18,
	0x04, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0c, 0x70, 0x6f, 0x73, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x5f, 0x0a, 0x12, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x30, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x11, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x36, 0x0a, 0x17, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x5f, 0x6e, 0x6f, 0x6e, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74,
	0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x0b, 0x6c, 0x6f, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x12,
	0x2a, 0x0a, 0x11, 0x6c, 0x6f, 0x67, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x5f,
	0x6e, 0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x6c, 0x6f, 0x67, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x73, 0x4e, 0x6f, 0x6e, 0x65, 0x12, 0x59, 0x0a, 0x12, 0x70,
	0x72, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61,
	0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d,
	0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x10, 0x70, 0x72, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61,
	0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x5b, 0x0a, 0x13, 0x70, 0x6f, 0x73, 0x74, 0x5f, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c,
	0x6f, 0x63, 0x6b, 0x2e, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x11, 0x70, 0x6f, 0x73, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e,
	0x63, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18, 0x09,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x3a, 0x0a, 0x19, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x77,
	0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0c, 0x52, 0x17, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x57,
	0x72, 0x69, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73,
	0x12, 0x3a, 0x0a, 0x19, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x64, 0x6f,
	0x6e, 0x6c, 0x79, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0c, 0x52, 0x17, 0x6c, 0x6f, 0x61, 0x64, 0x65, 0x64, 0x52, 0x65, 0x61, 0x64, 0x6f,
	0x6e, 0x6c, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x65, 0x73, 0x12, 0x4a, 0x0a, 0x0b,
	0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0a, 0x72, 0x65,
	0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x28, 0x0a, 0x10, 0x72, 0x65, 0x74, 0x75,
	0x72, 0x6e, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x5f, 0x6e, 0x6f, 0x6e, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x0e, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x4e, 0x6f,
	0x6e, 0x65, 0x12, 0x39, 0x0a, 0x16, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x75, 0x6e,
	0x69, 0x74, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x04, 0x48, 0x00, 0x52, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x55, 0x6e, 0x69,
	0x74, 0x73, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x64, 0x88, 0x01, 0x01, 0x42, 0x19, 0x0a,
	0x17, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x73, 0x5f,
	0x63, 0x6f, 0x6e, 0x73, 0x75, 0x6d, 0x65, 0x64, 0x22, 0x24, 0x0a, 0x10, 0x54, 0x72, 0x61, 0x6e,
	0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x10, 0x0a, 0x03,
	0x65, 0x72, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x65, 0x72, 0x72, 0x22, 0x7e,
	0x0a, 0x11, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0d, 0x52, 0x05, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x53, 0x0a, 0x0c, 0x69, 0x6e, 0x73,
	0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e,
	0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xa5,
	0x01, 0x0a, 0x10, 0x49, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69,
	0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70,
	0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a,
	0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52,
	0x08, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x26, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0d, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x73, 0x74, 0x61, 0x63, 0x6b, 0x5f,
	0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x6f, 0x0a, 0x13, 0x43, 0x6f, 0x6d, 0x70, 0x69, 0x6c,
	0x65, 0x64, 0x49, 0x6e, 0x73, 0x74, 0x72, 0x75, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x28, 0x0a,
	0x10, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x5f, 0x69, 0x6e, 0x64, 0x65,
	0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0e, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d,
	0x49, 0x64, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x0a, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x08, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xd2, 0x01, 0x0a, 0x0c, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x64, 0x65, 0x78, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x12, 0x0a,
	0x04, 0x6d, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6d, 0x69, 0x6e,
	0x74, 0x12, 0x54, 0x0a, 0x0f, 0x75, 0x69, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x6d,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x6f, 0x6c,
	0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x55, 0x69, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0d, 0x75, 0x69, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x12, 0x1d, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x22, 0x8a, 0x01, 0x0a,
	0x0d, 0x55, 0x69, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1b,
	0x0a, 0x09, 0x75, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x01, 0x52, 0x08, 0x75, 0x69, 0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64,
	0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x64,
	0x65, 0x63, 0x69, 0x6d, 0x61, 0x6c, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x28, 0x0a, 0x10, 0x75, 0x69, 0x5f, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x69, 0x41, 0x6d, 0x6f,
	0x75, 0x6e, 0x74, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x22, 0x3f, 0x0a, 0x0a, 0x52, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x67, 0x72,
	0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x09, 0x70, 0x72, 0x6f,
	0x67, 0x72, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xcb, 0x01, 0x0a, 0x06, 0x52,
	0x65, 0x77, 0x61, 0x72, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x75, 0x62, 0x6b, 0x65, 0x79, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x08, 0x6c, 0x61, 0x6d, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f, 0x73,
	0x74, 0x5f, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x0b, 0x70, 0x6f, 0x73, 0x74, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x4a, 0x0a, 0x0b,
	0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63,
	0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x6d,
	0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x6f,
	0x6d, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x9f, 0x01, 0x0a, 0x07, 0x52, 0x65, 0x77,
	0x61, 0x72, 0x64, 0x73, 0x12, 0x3f, 0x0a, 0x07, 0x72, 0x65, 0x77, 0x61, 0x72, 0x64, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x52, 0x07, 0x72, 0x65,
	0x77, 0x61, 0x72, 0x64, 0x73, 0x12, 0x53, 0x0a, 0x0e, 0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x61, 0x72,
	0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x73, 0x6f, 0x6c, 0x61, 0x6e, 0x61, 0x2e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x72, 0x6d, 0x65, 0x64, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x2e, 0x4e, 0x75,
	0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6e, 0x75, 0x6d,
	0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x2d, 0x0a, 0x0d, 0x55, 0x6e,
	0x69, 0x78, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0x30, 0x0a, 0x0b, 0x42, 0x6c, 0x6f,
	0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x62, 0x6c, 0x6f, 0x63,
	0x6b, 0x5f, 0x68, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0b,
	0x62, 0x6c, 0x6f, 0x63, 0x6b, 0x48, 0x65, 0x69, 0x67, 0x68, 0x74, 0x22, 0x36, 0x0a, 0x0d, 0x4e,
	0x75, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x6e, 0x75, 0x6d, 0x5f, 0x70, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x0d, 0x6e, 0x75, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2a, 0x49, 0x0a, 0x0a, 0x52, 0x65, 0x77, 0x61, 0x72, 0x64, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0f, 0x0a, 0x0b, 0x55, 0x6e, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x65, 0x64,
	0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x46, 0x65, 0x65, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x52,
	0x65, 0x6e, 0x74, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x74, 0x61, 0x6b, 0x69, 0x6e, 0x67,
	0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x56, 0x6f, 0x74, 0x69, 0x6e, 0x67, 0x10, 0x04, 0x42, 0x3b,
	0x5a, 0x39, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x70, 0x63,
	0x70, 0x6f, 0x6f, 0x6c, 0x2f, 0x79, 0x65, 0x6c, 0x6c, 0x6f, 0x77, 0x73, 0x74, 0x6f, 0x6e, 0x65,
	0x2d, 0x67, 0x72, 0x70, 0x63, 0x2f, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x73, 0x2f, 0x67,
	0x6f, 0x6c, 0x61, 0x6e, 0x67, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var (
	file_solana_storage_proto_rawDescOnce sync.Once
	file_solana_storage_proto_rawDescData = file_solana_storage_proto_rawDesc
)

func file_solana_storage_proto_rawDescGZIP() []byte {
	file_solana_storage_proto_rawDescOnce.Do(func() {
		file_solana_storage_proto_rawDescData = protoimpl.X.CompressGZIP(file_solana_storage_proto_rawDescData)
	})
	return file_solana_storage_proto_rawDescData
}

var file_solana_storage_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_solana_storage_proto_msgTypes = make([]protoimpl.MessageInfo, 19)
var file_solana_storage_proto_goTypes = []any{
	(RewardType)(0),                   // 0: solana.storage.ConfirmedBlock.RewardType
	(*ConfirmedBlock)(nil),            // 1: solana.storage.ConfirmedBlock.ConfirmedBlock
	(*ConfirmedTransaction)(nil),      // 2: solana.storage.ConfirmedBlock.ConfirmedTransaction
	(*Transaction)(nil),               // 3: solana.storage.ConfirmedBlock.Transaction
	(*Message)(nil),                   // 4: solana.storage.ConfirmedBlock.Message
	(*MessageHeader)(nil),             // 5: solana.storage.ConfirmedBlock.MessageHeader
	(*MessageAddressTableLookup)(nil), // 6: solana.storage.ConfirmedBlock.MessageAddressTableLookup
	(*TransactionStatusMeta)(nil),     // 7: solana.storage.ConfirmedBlock.TransactionStatusMeta
	(*TransactionError)(nil),          // 8: solana.storage.ConfirmedBlock.TransactionError
	(*InnerInstructions)(nil),         // 9: solana.storage.ConfirmedBlock.InnerInstructions
	(*InnerInstruction)(nil),          // 10: solana.storage.ConfirmedBlock.InnerInstruction
	(*CompiledInstruction)(nil),       // 11: solana.storage.ConfirmedBlock.CompiledInstruction
	(*TokenBalance)(nil),              // 12: solana.storage.ConfirmedBlock.TokenBalance
	(*UiTokenAmount)(nil),             // 13: solana.storage.ConfirmedBlock.UiTokenAmount
	(*ReturnData)(nil),                // 14: solana.storage.ConfirmedBlock.ReturnData
	(*Reward)(nil),                    // 15: solana.storage.ConfirmedBlock.Reward
	(*Rewards)(nil),                   // 16: solana.storage.ConfirmedBlock.Rewards
	(*UnixTimestamp)(nil),             // 17: solana.storage.ConfirmedBlock.UnixTimestamp
	(*BlockHeight)(nil),               // 18: solana.storage.ConfirmedBlock.BlockHeight
	(*NumPartitions)(nil),             // 19: solana.storage.ConfirmedBlock.NumPartitions
}
var file_solana_storage_proto_depIdxs = []int32{
	2,  // 0: solana.storage.ConfirmedBlock.ConfirmedBlock.transactions:type_name -> solana.storage.ConfirmedBlock.ConfirmedTransaction
	15, // 1: solana.storage.ConfirmedBlock.ConfirmedBlock.rewards:type_name -> solana.storage.ConfirmedBlock.Reward
	17, // 2: solana.storage.ConfirmedBlock.ConfirmedBlock.block_time:type_name -> solana.storage.ConfirmedBlock.UnixTimestamp
	18, // 3: solana.storage.ConfirmedBlock.ConfirmedBlock.block_height:type_name -> solana.storage.ConfirmedBlock.BlockHeight
	19, // 4: solana.storage.ConfirmedBlock.ConfirmedBlock.num_partitions:type_name -> solana.storage.ConfirmedBlock.NumPartitions
	3,  // 5: solana.storage.ConfirmedBlock.ConfirmedTransaction.transaction:type_name -> solana.storage.ConfirmedBlock.Transaction
	7,  // 6: solana.storage.ConfirmedBlock.ConfirmedTransaction.meta:type_name -> solana.storage.ConfirmedBlock.TransactionStatusMeta
	4,  // 7: solana.storage.ConfirmedBlock.Transaction.message:type_name -> solana.storage.ConfirmedBlock.Message
	5,  // 8: solana.storage.ConfirmedBlock.Message.header:type_name -> solana.storage.ConfirmedBlock.MessageHeader
	11, // 9: solana.storage.ConfirmedBlock.Message.instructions:type_name -> solana.storage.ConfirmedBlock.CompiledInstruction
	6,  // 10: solana.storage.ConfirmedBlock.Message.address_table_lookups:type_name -> solana.storage.ConfirmedBlock.MessageAddressTableLookup
	8,  // 11: solana.storage.ConfirmedBlock.TransactionStatusMeta.err:type_name -> solana.storage.ConfirmedBlock.TransactionError
	9,  // 12: solana.storage.ConfirmedBlock.TransactionStatusMeta.inner_instructions:type_name -> solana.storage.ConfirmedBlock.InnerInstructions
	12, // 13: solana.storage.ConfirmedBlock.TransactionStatusMeta.pre_token_balances:type_name -> solana.storage.ConfirmedBlock.TokenBalance
	12, // 14: solana.storage.ConfirmedBlock.TransactionStatusMeta.post_token_balances:type_name -> solana.storage.ConfirmedBlock.TokenBalance
	15, // 15: solana.storage.ConfirmedBlock.TransactionStatusMeta.rewards:type_name -> solana.storage.ConfirmedBlock.Reward
	14, // 16: solana.storage.ConfirmedBlock.TransactionStatusMeta.return_data:type_name -> solana.storage.ConfirmedBlock.ReturnData
	10, // 17: solana.storage.ConfirmedBlock.InnerInstructions.instructions:type_name -> solana.storage.ConfirmedBlock.InnerInstruction
	13, // 18: solana.storage.ConfirmedBlock.TokenBalance.ui_token_amount:type_name -> solana.storage.ConfirmedBlock.UiTokenAmount
	0,  // 19: solana.storage.ConfirmedBlock.Reward.reward_type:type_name -> solana.storage.ConfirmedBlock.RewardType
	15, // 20: solana.storage.ConfirmedBlock.Rewards.rewards:type_name -> solana.storage.ConfirmedBlock.Reward
	19, // 21: solana.storage.ConfirmedBlock.Rewards.num_partitions:type_name -> solana.storage.ConfirmedBlock.NumPartitions
	22, // [22:22] is the sub-list for method output_type
	22, // [22:22] is the sub-list for method input_type
	22, // [22:22] is the sub-list for extension type_name
	22, // [22:22] is the sub-list for extension extendee
	0,  // [0:22] is the sub-list for field type_name
}

func init() { file_solana_storage_proto_init() }
func file_solana_storage_proto_init() {
	if File_solana_storage_proto != nil {
		return
	}
	file_solana_storage_proto_msgTypes[6].OneofWrappers = []any{}
	file_solana_storage_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_solana_storage_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   19,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_solana_storage_proto_goTypes,
		DependencyIndexes: file_solana_storage_proto_depIdxs,
		EnumInfos:         file_solana_storage_proto_enumTypes,
		MessageInfos:      file_solana_storage_proto_msgTypes,
	}.Build()
	File_solana_storage_proto = out.File
	file_solana_storage_proto_rawDesc = nil
	file_solana_storage_proto_goTypes = nil
	file_solana_storage_proto_depIdxs = nil
}
