package service

import (
	"fmt"
	"os"
	"testing"
	"time"

	"model-processor/config"
	"model-processor/model"
	pb "model-processor/proto"

	"github.com/gagliardetto/solana-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/proto"
)

// MockKafkaProducer 模拟 Kafka 生产者
type MockKafkaProducer struct {
	mock.Mock
	Messages []MockMessage
}

type MockMessage struct {
	Key   []byte
	Value []byte
}

func (m *MockKafkaProducer) ProduceMessage(key, value []byte) error {
	m.Messages = append(m.Messages, MockMessage{Key: key, Value: value})
	args := m.Called(key, value)
	return args.Error(0)
}

func (m *MockKafkaProducer) ProduceMessageToTopic(topicType string, key, value []byte) error {
	m.Messages = append(m.Messages, MockMessage{Key: key, Value: value})
	args := m.Called(topicType, key, value)
	return args.Error(0)
}

// setupTestDatabase 设置测试数据库连接
func setupTestDatabase(dbType string) model.TimeSeriesDB {
	// 检查是否启用数据库测试
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		return nil
	}

	var db model.TimeSeriesDB
	var err error

	switch dbType {
	case "influxdb":
		db = &model.InfluxDBSimple{}
		testConfig := config.InfluxDBConfig{
			Host:         getEnvOrDefault("INFLUXDB_HOST", "localhost"),
			Port:         getEnvOrDefault("INFLUXDB_PORT", "8086"),
			Token:        getEnvOrDefault("INFLUXDB_TOKEN", "test-token-12345678901234567890"),
			Organization: getEnvOrDefault("INFLUXDB_ORG", "test-org"),
			Bucket:       getEnvOrDefault("INFLUXDB_BUCKET", "test-bucket"),
			Username:     getEnvOrDefault("INFLUXDB_USERNAME", "admin"),
			Password:     getEnvOrDefault("INFLUXDB_PASSWORD", "password123"),
		}
		err = db.Init(testConfig)
	case "tdengine":
		db = &model.TDengineDB{}
		testConfig := config.TDengineConfig{
			Host:     getEnvOrDefault("TDENGINE_HOST", "localhost"),
			Port:     getEnvOrDefault("TDENGINE_PORT", "6041"),
			User:     getEnvOrDefault("TDENGINE_USER", "root"),
			Password: getEnvOrDefault("TDENGINE_PASSWORD", "taosdata"),
			Database: getEnvOrDefault("TDENGINE_DATABASE", "test_db"),
		}
		err = db.Init(testConfig)
	default:
		return nil
	}

	if err != nil {
		return nil
	}

	// 验证数据库连接
	if err := db.HealthCheck(); err != nil {
		db.Close()
		return nil
	}

	return db
}

// getEnvOrDefault 获取环境变量值或返回默认值
func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

// createTestTransactionUpdate 创建测试用的交易数据
func createTestTransactionUpdate() ([]byte, error) {
	// 创建一个模拟的交易签名
	signature := make([]byte, 64)
	for i := range signature {
		signature[i] = byte(i % 256)
	}

	// 转换 uint32 到 byte slice
	accounts := make([]byte, 3)
	accounts[0] = 0
	accounts[1] = 1
	accounts[2] = 2

	// 创建交易结构
	transaction := &pb.Transaction{
		Signatures: [][]byte{signature},
		Message: &pb.Message{
			Header: &pb.MessageHeader{
				NumRequiredSignatures:       1,
				NumReadonlySignedAccounts:   0,
				NumReadonlyUnsignedAccounts: 1,
			},
			AccountKeys: [][]byte{
				// 用户地址
				solana.MustPublicKeyFromBase58("11111111111111111111111111111112").Bytes(),
				// WSOL 地址
				solana.MustPublicKeyFromBase58("So11111111111111111111111111111111111111112").Bytes(),
				// 代币地址
				solana.MustPublicKeyFromBase58("DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263").Bytes(),
			},
			RecentBlockhash: make([]byte, 32),
			Instructions: []*pb.CompiledInstruction{
				{
					ProgramIdIndex: 0,
					Accounts:       accounts,
					Data:           []byte("test-instruction-data"),
				},
			},
		},
	}

	// 创建交易信息
	transactionInfo := &pb.SubscribeUpdateTransactionInfo{
		Signature:   signature,
		IsVote:      false,
		Transaction: transaction,
		Meta: &pb.TransactionStatusMeta{
			Err: nil,
		},
		Index: 0,
	}

	// 创建交易更新
	transactionUpdate := &pb.SubscribeUpdateTransaction{
		Transaction: transactionInfo,
		Slot:        12345,
	}

	// 创建订阅更新
	update := &pb.SubscribeUpdate{
		UpdateOneof: &pb.SubscribeUpdate_Transaction{
			Transaction: transactionUpdate,
		},
	}

	// 序列化为字节
	return proto.Marshal(update)
}

func TestProcessUpdate_ValidTransaction(t *testing.T) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	// 创建真实的统计服务
	statsService := NewStatsService()

	// 尝试创建数据库连接
	db := setupTestDatabase(getEnvOrDefault("DB_TYPE", ""))

	// 创建交易服务
	service := NewTransactionService(mockKafkaProducer, statsService, db)

	// 确保测试结束时关闭数据库连接
	if db != nil {
		defer db.Close()
	}

	// 创建并设置地址服务
	addressService := NewAddressService()
	addressService.ProcessUpdate([]byte("test-group"), []byte(`["11111111111111111111111111111112"]`))
	service.SetAddressService(addressService)

	// 创建并设置代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 调用 ProcessUpdate
	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	// 等待异步处理完成
	time.Sleep(100 * time.Millisecond)

	if db != nil {
		t.Logf("测试完成：正常交易处理（使用 %s 数据库）", db.GetType())
	} else {
		t.Log("测试完成：正常交易处理（无数据库连接）")
	}
}

func TestProcessUpdate_InvalidData(t *testing.T) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	statsService := NewStatsService()

	// 创建交易服务
	service := NewTransactionService(mockKafkaProducer, statsService, nil)

	// 测试无效数据
	invalidData := []byte("invalid-protobuf-data")

	err := service.ProcessUpdate(invalidData, 0)
	assert.NoError(t, err) // 应该不返回错误，但会增加解析错误统计

	t.Log("测试完成：无效数据处理")
}

func TestProcessUpdate_NilTransaction(t *testing.T) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	statsService := NewStatsService()

	// 创建交易服务
	service := NewTransactionService(mockKafkaProducer, statsService, nil)

	// 创建空交易的更新
	update := &pb.SubscribeUpdate{
		UpdateOneof: &pb.SubscribeUpdate_Transaction{
			Transaction: &pb.SubscribeUpdateTransaction{
				Transaction: nil,
				Slot:        12345,
			},
		},
	}

	messageValue, err := proto.Marshal(update)
	assert.NoError(t, err)

	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	t.Log("测试完成：空交易处理")
}

func TestProcessUpdate_AddressFiltering(t *testing.T) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()

	// 创建交易服务
	service := NewTransactionService(mockKafkaProducer, statsService, nil)

	// 创建地址服务但不添加地址，测试过滤功能
	addressService := NewAddressService()
	service.SetAddressService(addressService)

	// 创建代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 调用 ProcessUpdate
	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	// 等待异步处理完成
	time.Sleep(100 * time.Millisecond)

	t.Log("测试完成：地址过滤")
}

func TestProcessUpdate_TokenMonitoring(t *testing.T) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()

	// 创建交易服务
	service := NewTransactionService(mockKafkaProducer, statsService, nil)

	// 创建代币服务并添加代币监控
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	tokenService.ProcessUpdate([]byte("So11111111111111111111111111111111111111112"), []byte("add"))
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 调用 ProcessUpdate
	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	// 等待异步处理完成
	time.Sleep(100 * time.Millisecond)

	t.Log("测试完成：代币监控")
}

// TestProcessUpdate_InfluxDBIntegration 专门测试 InfluxDB 集成
func TestProcessUpdate_InfluxDBIntegration(t *testing.T) {
	// 只有在启用数据库测试时才运行
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		t.Skip("跳过 InfluxDB 集成测试，设置 ENABLE_DB_TEST=true 启用")
	}

	// 创建 InfluxDB 连接
	db := setupTestDatabase("influxdb")
	if db == nil {
		t.Skip("跳过 InfluxDB 集成测试，无法连接到 InfluxDB")
	}
	defer db.Close()

	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()
	service := NewTransactionService(mockKafkaProducer, statsService, db)

	// 创建并设置地址服务
	addressService := NewAddressService()
	addressService.ProcessUpdate([]byte("test-group"), []byte(`["11111111111111111111111111111112"]`))
	service.SetAddressService(addressService)

	// 创建并设置代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 调用 ProcessUpdate - 这会触发数据库写入
	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	// 等待异步处理完成
	time.Sleep(3 * time.Second)

	// 验证数据库健康状态
	err = db.HealthCheck()
	assert.NoError(t, err)

	// 验证数据插入 - 查询刚刚插入的数据
	t.Run("VerifyDataInsertion", func(t *testing.T) {
		// 查询最近的交易记录
		endTime := time.Now().Add(1 * time.Minute)  // 给未来一点时间
		startTime := endTime.Add(-30 * time.Minute) // 查询最近30分钟的数据

		t.Logf("查询时间范围: %s 到 %s", startTime.Format("2006-01-02 15:04:05"), endTime.Format("2006-01-02 15:04:05"))

		transactions, err := db.QueryMemeTxByTimeRange(startTime, endTime, 20)
		if err != nil {
			t.Logf("查询数据时出错，可能是正常的（如果数据尚未写入）: %v", err)
		} else {
			t.Logf("成功查询到 %d 条交易记录", len(transactions))

			// 如果查询到数据，验证数据结构
			for i, tx := range transactions {
				t.Logf("交易 %d: TxHash=%s, UserAddr=%s, TxType=%s, BlockSlot=%d, Ts=%s",
					i+1, tx.TxHash, tx.UserAddr, tx.TxType, tx.BlockSlot, tx.Ts.Format("2006-01-02 15:04:05"))

				// 验证基本字段不为空
				if tx.TxHash != "" {
					assert.NotEmpty(t, tx.TxHash, "交易哈希不应为空")
				}
				if tx.BlockSlot != 0 {
					assert.NotZero(t, tx.BlockSlot, "区块高度不应为0")
				}

				if i >= 4 { // 只验证前几条记录
					break
				}
			}
		}

		// 额外测试：尝试使用更宽的时间范围查询
		t.Logf("尝试更宽的时间范围查询...")
		wideStartTime := time.Now().Add(-24 * time.Hour)
		wideEndTime := time.Now().Add(1 * time.Hour)
		wideTransactions, wideErr := db.QueryMemeTxByTimeRange(wideStartTime, wideEndTime, 50)
		if wideErr != nil {
			t.Logf("宽范围查询出错: %v", wideErr)
		} else {
			t.Logf("宽范围查询结果：找到 %d 条记录", len(wideTransactions))
		}
	})

	// 验证特定哈希查询
	t.Run("VerifyHashQuery", func(t *testing.T) {
		// 使用一个测试哈希查询（即使没有结果也能验证查询功能）
		testHash := "test_hash_12345"
		transactions, err := db.QueryMemeTxByHash(testHash)

		// 查询不应该返回错误（即使没有结果）
		assert.NoError(t, err, "按哈希查询不应返回错误")
		t.Logf("按哈希查询结果：找到 %d 条记录", len(transactions))

		// 测试查询一个实际可能存在的哈希
		if influxDB, ok := db.(*model.InfluxDBSimple); ok {
			// 尝试健康检查来验证连接
			healthErr := influxDB.HealthCheck()
			t.Logf("InfluxDB 健康检查结果: %v", healthErr)
		}
	})

	// 验证用户地址查询
	t.Run("VerifyUserQuery", func(t *testing.T) {
		testUserAddr := "11111111111111111111111111111112"
		transactions, err := db.QueryMemeTxByUser(testUserAddr, 10)

		assert.NoError(t, err, "按用户地址查询不应返回错误")
		t.Logf("按用户地址查询结果：找到 %d 条记录", len(transactions))

		// 如果找到记录，验证用户地址是否匹配
		for i, tx := range transactions {
			if tx.UserAddr != "" {
				t.Logf("记录 %d: UserAddr=%s, TxHash=%s", i+1, tx.UserAddr, tx.TxHash)
				assert.Equal(t, testUserAddr, tx.UserAddr, "查询结果的用户地址应该匹配")
			}
		}

		// 尝试查询空地址
		emptyTransactions, emptyErr := db.QueryMemeTxByUser("", 5)
		assert.NoError(t, emptyErr, "查询空用户地址不应返回错误")
		t.Logf("空用户地址查询结果：找到 %d 条记录", len(emptyTransactions))
	})

	// 验证代币地址查询
	t.Run("VerifyTokenQuery", func(t *testing.T) {
		testTokenAddr := "So11111111111111111111111111111111111111112"
		transactions, err := db.QueryMemeTxByToken(testTokenAddr, 10)

		assert.NoError(t, err, "按代币地址查询不应返回错误")
		t.Logf("按代币地址查询结果：找到 %d 条记录", len(transactions))

		// 测试不同的代币地址
		altTokenAddr := "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
		altTransactions, altErr := db.QueryMemeTxByToken(altTokenAddr, 5)
		assert.NoError(t, altErr, "查询替代代币地址不应返回错误")
		t.Logf("替代代币地址查询结果：找到 %d 条记录", len(altTransactions))
	})

	t.Logf("测试完成：InfluxDB 集成测试 (数据库类型: %s)", db.GetType())
}

// TestProcessUpdate_InfluxDBDataValidation 专门测试 InfluxDB 数据验证
func TestProcessUpdate_InfluxDBDataValidation(t *testing.T) {
	// 只有在启用数据库测试时才运行
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		t.Skip("跳过 InfluxDB 数据验证测试，设置 ENABLE_DB_TEST=true 启用")
	}

	// 创建 InfluxDB 连接
	db := setupTestDatabase("influxdb")
	if db == nil {
		t.Skip("跳过 InfluxDB 数据验证测试，无法连接到 InfluxDB")
	}
	defer db.Close()

	// 测试直接数据库插入和查询
	t.Run("DirectDatabaseInsertAndQuery", func(t *testing.T) {
		// 创建测试交易数据
		testTx := model.MemeTx{
			Ts:               time.Now(),
			BlockTime:        time.Now().Unix(),
			TxHash:           "test_direct_insert_" + time.Now().Format("20060102_150405"),
			UserAddr:         "11111111111111111111111111111112",
			TokenInAddr:      "So11111111111111111111111111111111111111112",
			TokenOutAddr:     "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
			AmountIn:         "1000000",
			AmountOut:        "500000",
			TxType:           "BUY",
			BlockSlot:        12345,
			InstructionIndex: 0,
			TxIndex:          1,
		}

		// 插入数据
		err := db.InsertMemeTx(testTx, "solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263")
		assert.NoError(t, err, "直接插入数据不应返回错误")

		// 等待数据写入
		time.Sleep(2 * time.Second)

		// 查询验证插入的数据
		transactions, err := db.QueryMemeTxByHash(testTx.TxHash)
		assert.NoError(t, err, "查询插入的数据不应返回错误")

		t.Logf("查询到 %d 条记录，期望找到刚插入的记录", len(transactions))

		// 如果找不到具体哈希，尝试时间范围查询
		if len(transactions) == 0 {
			t.Logf("按哈希查询未找到记录，尝试按时间范围查询...")
			endTime := time.Now().Add(1 * time.Minute)
			startTime := endTime.Add(-10 * time.Minute)
			timeRangeTransactions, timeErr := db.QueryMemeTxByTimeRange(startTime, endTime, 20)
			if timeErr != nil {
				t.Logf("时间范围查询出错: %v", timeErr)
			} else {
				t.Logf("时间范围查询找到 %d 条记录", len(timeRangeTransactions))
				for i, tx := range timeRangeTransactions {
					if tx.TxHash == testTx.TxHash {
						t.Logf("在时间范围查询中找到目标记录！")
						transactions = append(transactions, tx)
						break
					}
					if i < 3 { // 只显示前几条记录
						t.Logf("时间范围记录 %d: TxHash=%s, Ts=%s",
							i+1, tx.TxHash, tx.Ts.Format("2006-01-02 15:04:05"))
					}
				}
			}
		}

		// 如果找到记录，验证数据的正确性
		if len(transactions) > 0 {
			found := false
			for _, tx := range transactions {
				if tx.TxHash == testTx.TxHash {
					found = true
					t.Logf("找到目标记录，开始验证数据...")
					assert.Equal(t, testTx.UserAddr, tx.UserAddr, "用户地址应该匹配")
					assert.Equal(t, testTx.TxType, tx.TxType, "交易类型应该匹配")
					assert.Equal(t, testTx.BlockSlot, tx.BlockSlot, "区块高度应该匹配")
					assert.Equal(t, testTx.AmountIn, tx.AmountIn, "输入金额应该匹配")
					assert.Equal(t, testTx.AmountOut, tx.AmountOut, "输出金额应该匹配")
					t.Logf("数据验证成功：TxHash=%s, UserAddr=%s, TxType=%s, AmountIn=%s, AmountOut=%s",
						tx.TxHash, tx.UserAddr, tx.TxType, tx.AmountIn, tx.AmountOut)
					break
				}
			}

			if !found {
				t.Logf("警告：未找到期望的交易记录，这可能是由于数据延迟或查询限制")
				// 显示实际找到的记录
				for i, tx := range transactions {
					if i < 3 {
						t.Logf("实际记录 %d: TxHash=%s", i+1, tx.TxHash)
					}
				}
			}
		} else {
			t.Logf("警告：查询未返回任何记录，这可能是由于数据延迟或 InfluxDB 配置问题")

			// 额外的调试：尝试查询所有最近的记录
			t.Logf("尝试查询所有最近记录进行调试...")
			debugStartTime := time.Now().Add(-1 * time.Hour)
			debugEndTime := time.Now().Add(1 * time.Hour)
			debugTransactions, debugErr := db.QueryMemeTxByTimeRange(debugStartTime, debugEndTime, 100)
			if debugErr != nil {
				t.Logf("调试查询出错: %v", debugErr)
			} else {
				t.Logf("调试查询找到 %d 条总记录", len(debugTransactions))
				for i, tx := range debugTransactions {
					if i < 5 { // 只显示前5条
						t.Logf("调试记录 %d: TxHash=%s, UserAddr=%s, Ts=%s",
							i+1, tx.TxHash, tx.UserAddr, tx.Ts.Format("2006-01-02 15:04:05"))
					}
				}
			}
		}
	})

	// 测试批量插入
	t.Run("BatchInsertAndQuery", func(t *testing.T) {
		batchCount := 5
		testHashes := make([]string, batchCount)

		// 批量插入数据
		for i := 0; i < batchCount; i++ {
			testTx := model.MemeTx{
				Ts:               time.Now().Add(time.Duration(i) * time.Millisecond),
				BlockTime:        time.Now().Unix() + int64(i),
				TxHash:           fmt.Sprintf("test_batch_%d_%d", time.Now().Unix(), i),
				UserAddr:         "11111111111111111111111111111112",
				TokenInAddr:      "So11111111111111111111111111111111111111112",
				TokenOutAddr:     "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
				AmountIn:         fmt.Sprintf("%d", 1000000+i*100000),
				AmountOut:        fmt.Sprintf("%d", 500000+i*50000),
				TxType:           "BUY",
				BlockSlot:        12345 + int64(i),
				InstructionIndex: int32(i),
				TxIndex:          int64(i),
			}

			testHashes[i] = testTx.TxHash

			// 使用批量插入方法
			err := db.InsertMemeTxBatch(testTx, "solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", i)
			assert.NoError(t, err, "批量插入第 %d 条记录不应返回错误", i)
		}

		// 等待批量数据写入完成
		time.Sleep(5 * time.Second)

		// 验证批量插入的数据
		foundCount := 0
		notFoundHashes := []string{}

		t.Logf("开始验证 %d 条批量插入的记录...", batchCount)

		for i, hash := range testHashes {
			transactions, err := db.QueryMemeTxByHash(hash)
			assert.NoError(t, err, "查询批量插入的记录 %d 不应返回错误", i)

			if len(transactions) > 0 {
				foundCount++
				t.Logf("✓ 找到批量插入的记录 %d: %s", i, hash)

				// 验证第一条记录的数据
				tx := transactions[0]
				if tx.TxHash == hash {
					expectedAmountIn := fmt.Sprintf("%d", 1000000+i*100000)
					expectedAmountOut := fmt.Sprintf("%d", 500000+i*50000)

					if tx.AmountIn == expectedAmountIn && tx.AmountOut == expectedAmountOut {
						t.Logf("  ✓ 数据验证通过: AmountIn=%s, AmountOut=%s", tx.AmountIn, tx.AmountOut)
					} else {
						t.Logf("  ⚠ 数据不匹配: 期望 AmountIn=%s, AmountOut=%s; 实际 AmountIn=%s, AmountOut=%s",
							expectedAmountIn, expectedAmountOut, tx.AmountIn, tx.AmountOut)
					}
				}
			} else {
				notFoundHashes = append(notFoundHashes, hash)
				t.Logf("✗ 未找到批量插入的记录 %d: %s", i, hash)
			}
		}

		t.Logf("批量插入验证：插入 %d 条，查询到 %d 条", batchCount, foundCount)

		if foundCount > 0 {
			t.Logf("✓ 成功验证了 %d/%d 条记录", foundCount, batchCount)
		}

		if len(notFoundHashes) > 0 {
			t.Logf("未找到的哈希 (%d 个): %v", len(notFoundHashes), notFoundHashes)

			// 尝试通过时间范围查询来查找未找到的记录
			t.Logf("尝试通过时间范围查询来查找未找到的记录...")
			endTime := time.Now().Add(1 * time.Minute)
			startTime := endTime.Add(-15 * time.Minute)
			timeRangeTransactions, timeErr := db.QueryMemeTxByTimeRange(startTime, endTime, 50)

			if timeErr != nil {
				t.Logf("时间范围查询出错: %v", timeErr)
			} else {
				t.Logf("时间范围查询找到 %d 条记录", len(timeRangeTransactions))

				// 检查未找到的哈希是否在时间范围查询结果中
				timeFoundCount := 0
				for _, notFoundHash := range notFoundHashes {
					for _, tx := range timeRangeTransactions {
						if tx.TxHash == notFoundHash {
							timeFoundCount++
							t.Logf("✓ 在时间范围查询中找到: %s", notFoundHash)
							break
						}
					}
				}
				t.Logf("时间范围查询额外找到 %d 条记录", timeFoundCount)
			}
		}

		// 至少应该能找到一些记录（考虑到 InfluxDB 的异步特性）
		if foundCount == 0 {
			t.Logf("警告：未找到任何批量插入的记录，这可能是正常的延迟")
		}
	})

	// 测试数据类型和边界条件
	t.Run("DataTypesAndBoundaries", func(t *testing.T) {
		// 测试大数值
		testTx := model.MemeTx{
			Ts:               time.Now(),
			BlockTime:        time.Now().Unix(),
			TxHash:           "test_large_numbers_" + time.Now().Format("20060102_150405"),
			UserAddr:         "11111111111111111111111111111112",
			TokenInAddr:      "So11111111111111111111111111111111111111112",
			TokenOutAddr:     "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
			AmountIn:         "999999999999999999",
			AmountOut:        "1",
			TxType:           "SELL",
			BlockSlot:        999999999,
			InstructionIndex: 2147483647,          // max int32
			TxIndex:          9223372036854775807, // max int64
		}

		err := db.InsertMemeTx(testTx, "solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263")
		assert.NoError(t, err, "插入大数值数据不应返回错误")

		// 测试空字符串和特殊字符
		testTx2 := model.MemeTx{
			Ts:               time.Now(),
			BlockTime:        time.Now().Unix(),
			TxHash:           "test_special_chars_" + time.Now().Format("20060102_150405"),
			UserAddr:         "", // 空字符串
			TokenInAddr:      "So11111111111111111111111111111111111111112",
			TokenOutAddr:     "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
			AmountIn:         "0",
			AmountOut:        "0",
			TxType:           "SWAP",
			BlockSlot:        0,
			InstructionIndex: -1,
			TxIndex:          0,
		}

		err = db.InsertMemeTx(testTx2, "solana", "")
		assert.NoError(t, err, "插入特殊字符数据不应返回错误")

		t.Logf("边界条件测试完成")
	})
}

// TestProcessUpdate_TDengineIntegration 专门测试 TDengine 集成
func TestProcessUpdate_TDengineIntegration(t *testing.T) {
	// 只有在启用数据库测试时才运行
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		t.Skip("跳过 TDengine 集成测试，设置 ENABLE_DB_TEST=true 启用")
	}

	// 创建 TDengine 连接
	db := setupTestDatabase("tdengine")
	if db == nil {
		t.Skip("跳过 TDengine 集成测试，无法连接到 TDengine")
	}
	defer db.Close()

	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()
	service := NewTransactionService(mockKafkaProducer, statsService, db)

	// 创建并设置地址服务
	addressService := NewAddressService()
	addressService.ProcessUpdate([]byte("test-group"), []byte(`["11111111111111111111111111111112"]`))
	service.SetAddressService(addressService)

	// 创建并设置代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 调用 ProcessUpdate
	err = service.ProcessUpdate(messageValue, 0)
	assert.NoError(t, err)

	// 等待异步处理完成
	time.Sleep(2 * time.Second)

	// 验证数据库健康状态
	err = db.HealthCheck()
	assert.NoError(t, err)

	t.Logf("测试完成：TDengine 集成测试 (数据库类型: %s)", db.GetType())
}

// TestProcessUpdate_DatabaseBenchmark 数据库性能基准测试
func TestProcessUpdate_DatabaseBenchmark(t *testing.T) {
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		t.Skip("跳过数据库性能测试，设置 ENABLE_DB_TEST=true 启用")
	}

	// 获取数据库类型
	dbType := getEnvOrDefault("DB_TYPE", "influxdb")
	db := setupTestDatabase(dbType)
	if db == nil {
		t.Skipf("跳过数据库性能测试，无法连接到 %s", dbType)
	}
	defer db.Close()

	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()
	service := NewTransactionService(mockKafkaProducer, statsService, db)

	// 创建并设置地址服务
	addressService := NewAddressService()
	addressService.ProcessUpdate([]byte("test-group"), []byte(`["11111111111111111111111111111112"]`))
	service.SetAddressService(addressService)

	// 创建并设置代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	assert.NoError(t, err)

	// 性能测试
	startTime := time.Now()
	testCount := 100

	for i := 0; i < testCount; i++ {
		err = service.ProcessUpdate(messageValue, 0)
		assert.NoError(t, err)
	}

	// 等待所有异步处理完成
	time.Sleep(5 * time.Second)

	duration := time.Since(startTime)
	rate := float64(testCount) / duration.Seconds()

	t.Logf("数据库性能测试结果 (%s):", dbType)
	t.Logf("- 处理交易数: %d", testCount)
	t.Logf("- 总耗时: %v", duration)
	t.Logf("- 处理速率: %.2f 交易/秒", rate)
}

func BenchmarkProcessUpdate(b *testing.B) {
	// 创建 mock 对象
	mockKafkaProducer := &MockKafkaProducer{}
	mockKafkaProducer.On("ProduceMessage", mock.Anything, mock.Anything).Return(nil)

	statsService := NewStatsService()
	service := NewTransactionService(mockKafkaProducer, statsService, nil)

	// 创建并设置地址服务
	addressService := NewAddressService()
	addressService.ProcessUpdate([]byte("test-group"), []byte(`["11111111111111111111111111111112"]`))
	service.SetAddressService(addressService)

	// 创建并设置代币服务
	tokenService := NewTokenService(mockKafkaProducer, "test-topic")
	service.SetTokenService(tokenService)

	// 创建测试交易数据
	messageValue, err := createTestTransactionUpdate()
	if err != nil {
		b.Fatal(err)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.ProcessUpdate(messageValue, 0)
	}
}

// TestQueryMemeTxByHash_Fix 专门测试 QueryMemeTxByHash 修复
func TestQueryMemeTxByHash_Fix(t *testing.T) {
	// 只有在启用数据库测试时才运行
	if os.Getenv("ENABLE_DB_TEST") != "true" {
		t.Skip("跳过 QueryMemeTxByHash 修复测试，设置 ENABLE_DB_TEST=true 启用")
	}

	// 创建 InfluxDB 连接
	db := setupTestDatabase("influxdb")
	if db == nil {
		t.Skip("跳过 QueryMemeTxByHash 修复测试，无法连接到 InfluxDB")
	}
	defer db.Close()

	// 创建一个唯一的测试交易
	testTx := model.MemeTx{
		Ts:               time.Now(),
		BlockTime:        time.Now().Unix(),
		TxHash:           "hash_fix_test_" + time.Now().Format("20060102_150405_000"),
		UserAddr:         "TestUser1111111111111111111111111112",
		TokenInAddr:      "So11111111111111111111111111111111111111112",
		TokenOutAddr:     "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",
		AmountIn:         "1000000",
		AmountOut:        "500000",
		TxType:           "BUY",
		BlockSlot:        12345,
		InstructionIndex: 0,
		TxIndex:          1,
	}

	t.Logf("测试哈希: %s", testTx.TxHash)

	// 直接插入数据
	err := db.InsertMemeTx(testTx, "solana", "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263")
	assert.NoError(t, err, "插入测试数据不应返回错误")

	// 等待数据写入完成
	t.Logf("等待数据写入完成...")
	time.Sleep(3 * time.Second)

	// 测试 QueryMemeTxByHash
	t.Run("TestQueryMemeTxByHashDirect", func(t *testing.T) {
		t.Logf("直接测试 QueryMemeTxByHash，查询哈希: %s", testTx.TxHash)

		transactions, err := db.QueryMemeTxByHash(testTx.TxHash)
		assert.NoError(t, err, "QueryMemeTxByHash 不应返回错误")

		t.Logf("QueryMemeTxByHash 返回 %d 条记录", len(transactions))

		if len(transactions) > 0 {
			t.Logf("✅ QueryMemeTxByHash 修复成功！找到了 %d 条记录", len(transactions))

			// 验证返回的数据
			found := false
			for i, tx := range transactions {
				t.Logf("记录 %d: TxHash=%s, UserAddr=%s, TxType=%s, AmountIn=%s",
					i+1, tx.TxHash, tx.UserAddr, tx.TxType, tx.AmountIn)

				if tx.TxHash == testTx.TxHash {
					found = true
					t.Logf("✅ 找到目标记录，开始数据验证...")

					// 验证关键字段
					assert.Equal(t, testTx.TxHash, tx.TxHash, "TxHash应该匹配")
					assert.Equal(t, testTx.UserAddr, tx.UserAddr, "UserAddr应该匹配")
					assert.Equal(t, testTx.TxType, tx.TxType, "TxType应该匹配")
					assert.Equal(t, testTx.AmountIn, tx.AmountIn, "AmountIn应该匹配")
					assert.Equal(t, testTx.AmountOut, tx.AmountOut, "AmountOut应该匹配")
					assert.Equal(t, testTx.BlockSlot, tx.BlockSlot, "BlockSlot应该匹配")

					t.Logf("✅ 所有字段验证通过！")
					break
				}
			}

			assert.True(t, found, "应该找到目标交易记录")
		} else {
			t.Logf("❌ QueryMemeTxByHash 仍然返回0条记录")

			// 尝试时间范围查询作为对比
			t.Logf("尝试时间范围查询作为对比...")
			endTime := time.Now().Add(1 * time.Minute)
			startTime := endTime.Add(-10 * time.Minute)
			timeRangeTransactions, timeErr := db.QueryMemeTxByTimeRange(startTime, endTime, 20)

			if timeErr != nil {
				t.Logf("时间范围查询也失败: %v", timeErr)
			} else {
				t.Logf("时间范围查询找到 %d 条记录", len(timeRangeTransactions))

				// 检查目标哈希是否在时间范围查询结果中
				foundInTimeRange := false
				for _, tx := range timeRangeTransactions {
					if tx.TxHash == testTx.TxHash {
						foundInTimeRange = true
						t.Logf("✅ 在时间范围查询中找到目标记录: %s", tx.TxHash)
						break
					}
				}

				if !foundInTimeRange {
					t.Logf("❌ 时间范围查询中也没有找到目标记录")
				}
			}
		}
	})

	// 额外测试：查询一个不存在的哈希
	t.Run("TestQueryNonExistentHash", func(t *testing.T) {
		nonExistentHash := "non_existent_hash_12345"
		transactions, err := db.QueryMemeTxByHash(nonExistentHash)
		assert.NoError(t, err, "查询不存在的哈希不应返回错误")
		assert.Equal(t, 0, len(transactions), "查询不存在的哈希应该返回0条记录")
		t.Logf("✅ 查询不存在哈希测试通过: %d 条记录", len(transactions))
	})

	// 额外测试：查询空哈希
	t.Run("TestQueryEmptyHash", func(t *testing.T) {
		transactions, err := db.QueryMemeTxByHash("")
		assert.NoError(t, err, "查询空哈希不应返回错误")
		t.Logf("查询空哈希结果: %d 条记录", len(transactions))
	})
}
