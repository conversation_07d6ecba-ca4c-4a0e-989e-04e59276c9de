package txparser

import (
	"fmt"
	"model-processor/proto"

	ag_binary "github.com/gagliardetto/binary"
	"github.com/gagliardetto/solana-go"
	"github.com/mr-tron/base58"
)

var (
	PumpfunTradeEventDiscriminator  = [16]byte{228, 69, 165, 46, 81, 203, 154, 29, 189, 219, 127, 211, 78, 230, 97, 238}
	PumpfunCreateEventDiscriminator = [16]byte{228, 69, 165, 46, 81, 203, 154, 29, 27, 114, 169, 77, 222, 235, 99, 118}
)

type PumpfunTradeEvent struct {
	Mint                  solana.PublicKey // 代币的 Mint 地址
	SolAmount             uint64           // 交易中涉及的 SOL 数量（最小单位）
	TokenAmount           uint64           // 交易中涉及的代币数量（最小单位）
	IsBuy                 bool             // 是否为购买操作
	User                  solana.PublicKey // 用户的钱包地址
	Timestamp             int64            // 交易的时间戳（Unix 时间）
	VirtualSolReserves    uint64           // 虚拟的 SOL 储备量
	VirtualTokenReserves  uint64           // 虚拟的代币储备量
	RealSolReserves       uint64           // 实际的 SOL 储备量
	RealTokenReserves     uint64           // 实际的代币储备量
	FeeRecipient          solana.PublicKey // 接收手续费的地址
	FeeBasisPoints        uint64           // 手续费的基点数（例如，95 表示 0.95%）
	Fee                   uint64           // 实际收取的手续费金额
	Creator               solana.PublicKey // 创建者的钱包地址
	CreatorFeeBasisPoints uint64           // 创建者手续费的基点数（例如，5 表示 0.05%）
	CreatorFee            uint64           // 实际支付给创建者的手续费金额
}

type PumpfunCreateEvent struct {
	Name         string
	Symbol       string
	Uri          string
	Mint         solana.PublicKey
	BondingCurve solana.PublicKey
	User         solana.PublicKey
}

func (p *Parser) processPumpfunSwaps(instructionIndex int) ([]SwapData, []CreateData) {
	var swaps []SwapData
	var createSwaps []CreateData
	for _, innerInstructionSet := range p.txMeta.InnerInstructions {
		if innerInstructionSet.Index == uint32(instructionIndex) {
			for _, innerInstruction := range innerInstructionSet.Instructions {
				if p.isPumpFunTradeEventInstruction(innerInstruction) {
					eventData, err := p.parsePumpfunTradeEventInstruction(innerInstruction)
					if err != nil {
						p.Log.Errorf("error processing Pumpfun trade event: %s", err)
					}
					if eventData != nil {
						if eventData.IsBuy {
							swaps = append(swaps, SwapData{Type: PUMP_FUN, TxType: "BUY", InstructionIndex: instructionIndex, Data: eventData})
						} else {
							swaps = append(swaps, SwapData{Type: PUMP_FUN, TxType: "SELL", InstructionIndex: instructionIndex, Data: eventData})
						}
					}
				} else if p.isPumpFunCreateEventInstruction(innerInstruction) {
					eventData, err := p.parsePumpfunCreateEventInstruction(innerInstruction)
					if err != nil {
						p.Log.Errorf("error processing Pumpfun trade event: %s", err)
					}
					if eventData != nil {
						createSwaps = append(createSwaps, CreateData{Type: PUMP_FUN_CREATE, InstructionIndex: instructionIndex, Data: eventData})
					}
				} else if p.isPumpSwapCreateEventInstruction(innerInstruction) {
					eventData, err := p.parsePumpSwapEventInstruction(innerInstruction)
					if err != nil {
						p.Log.Errorf("error processing Pumpfun trade event: %s", err)
					}
					if eventData != nil {
						swaps = append(swaps, SwapData{Type: PUMP_SWAP_CREATE, Data: eventData})
					}
				}
			}
		}
	}
	return swaps, createSwaps
}

func (p *Parser) parsePumpfunTradeEventInstruction(instruction *proto.InnerInstruction) (*PumpfunTradeEvent, error) {
	decodedBytes, err := base58.Decode(base58.Encode(instruction.Data))
	if err != nil {
		return nil, fmt.Errorf("error decoding instruction data: %s", err)
	}
	decoder := ag_binary.NewBorshDecoder(decodedBytes[16:])

	return handlePumpfunTradeEvent(decoder)
}

func (p *Parser) parsePumpfunCreateEventInstruction(instruction *proto.InnerInstruction) (*PumpfunCreateEvent, error) {
	decodedBytes, err := base58.Decode(base58.Encode(instruction.Data))
	if err != nil {
		return nil, fmt.Errorf("error decoding instruction data: %s", err)
	}
	decoder := ag_binary.NewBorshDecoder(decodedBytes[16:])

	return handlePumpfunCreateEvent(decoder)
}

func handlePumpfunTradeEvent(decoder *ag_binary.Decoder) (*PumpfunTradeEvent, error) {
	var trade PumpfunTradeEvent
	if err := decoder.Decode(&trade); err != nil {
		return nil, fmt.Errorf("error unmarshaling TradeEvent: %s", err)
	}

	return &trade, nil
}

func handlePumpfunCreateEvent(decoder *ag_binary.Decoder) (*PumpfunCreateEvent, error) {
	var create PumpfunCreateEvent
	if err := decoder.Decode(&create); err != nil {
		return nil, fmt.Errorf("error unmarshaling TradeEvent: %s", err)
	}

	return &create, nil
}
