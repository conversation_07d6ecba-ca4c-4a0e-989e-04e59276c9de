# Solana 交易处理系统 - 业务逻辑详细文档

## 1. 业务概述

### 1.1 核心业务场景
本系统主要服务于以下业务场景：
- **DEX 交易监控**: 实时监控 Solana 网络上的去中心化交易所交易
- **Meme 币追踪**: 专注于新兴 Meme 代币的交易活动监控
- **用户行为分析**: 分析用户的交易模式和偏好
- **市场数据服务**: 为交易平台和分析工具提供实时数据
- **风险监控**: 识别异常交易和潜在风险

### 1.2 支持的业务类型
- **现货交易**: 代币与代币之间的直接交换
- **流动性操作**: 添加和移除流动性池
- **套利交易**: 跨平台价格差异套利
- **新币发行**: Pump.fun 等平台的新币创建和交易
- **聚合交易**: Jupiter 等聚合器的复合交易

## 2. 协议业务逻辑

### 2.1 Raydium 协议

#### 2.1.1 业务特点
- **AMM 模式**: 自动做市商机制
- **流动性池**: 基于恒定乘积公式
- **手续费机制**: 0.25% 交易手续费
- **LP 代币**: 流动性提供者代币奖励

#### 2.1.2 交易类型识别
```go
func (p *Parser) processRaydSwaps(instructionIndex int) []SwapData {
    var swaps []SwapData
    
    for _, innerInstructionSet := range p.txMeta.InnerInstructions {
        if innerInstructionSet.Index == uint32(instructionIndex) {
            for _, innerInstruction := range innerInstructionSet.Instructions {
                switch {
                case p.isTransfer(innerInstruction):
                    // 处理代币转账指令
                    transfer := p.processTransfer(innerInstruction)
                    if transfer != nil {
                        swaps = append(swaps, SwapData{
                            Type: RAYDIUM,
                            InstructionIndex: instructionIndex,
                            Data: transfer,
                        })
                    }
                case p.isTransferCheck(innerInstruction):
                    // 处理带检查的代币转账指令
                    transfer := p.processTransferCheck(innerInstruction)
                    if transfer != nil {
                        swaps = append(swaps, SwapData{
                            Type: RAYDIUM,
                            InstructionIndex: instructionIndex,
                            Data: transfer,
                        })
                    }
                }
            }
        }
    }
    
    return swaps
}
```

#### 2.1.3 业务数据提取
- **交易对信息**: 提取 tokenA 和 tokenB 地址
- **交易金额**: 计算输入和输出代币数量
- **价格影响**: 计算交易对价格的影响
- **滑点分析**: 分析实际执行价格与预期价格的差异

### 2.2 Pump.fun 协议

#### 2.2.1 业务特点
- **Meme 币发行**: 专门用于 Meme 代币的创建和交易
- **绑定曲线**: 基于绑定曲线的价格发现机制
- **创建者奖励**: 代币创建者获得手续费分成
- **社区驱动**: 社区投票和参与机制

#### 2.2.2 事件处理逻辑
```go
type PumpfunTradeEvent struct {
    Mint                  solana.PublicKey // 代币的 Mint 地址
    SolAmount             uint64           // 交易中涉及的 SOL 数量
    TokenAmount           uint64           // 交易中涉及的代币数量
    IsBuy                 bool             // 是否为购买操作
    User                  solana.PublicKey // 用户的钱包地址
    Timestamp             int64            // 交易的时间戳
    VirtualSolReserves    uint64           // 虚拟的 SOL 储备量
    VirtualTokenReserves  uint64           // 虚拟的代币储备量
    RealSolReserves       uint64           // 实际的 SOL 储备量
    RealTokenReserves     uint64           // 实际的代币储备量
    FeeRecipient          solana.PublicKey // 接收手续费的地址
    FeeBasisPoints        uint64           // 手续费的基点数
    Fee                   uint64           // 实际收取的手续费金额
    Creator               solana.PublicKey // 创建者的钱包地址
    CreatorFeeBasisPoints uint64           // 创建者手续费的基点数
    CreatorFee            uint64           // 实际支付给创建者的手续费金额
}

func (p *Parser) processPumpfunSwaps(instructionIndex int) ([]SwapData, []CreateData) {
    var swaps []SwapData
    var createSwaps []CreateData
    
    for _, innerInstructionSet := range p.txMeta.InnerInstructions {
        if innerInstructionSet.Index == uint32(instructionIndex) {
            for _, innerInstruction := range innerInstructionSet.Instructions {
                if p.isPumpFunTradeEventInstruction(innerInstruction) {
                    eventData, err := p.parsePumpfunTradeEventInstruction(innerInstruction)
                    if err != nil {
                        p.Log.Errorf("error processing Pumpfun trade event: %s", err)
                        continue
                    }
                    
                    if eventData != nil {
                        swapData := convertPumpfunToSwapData(eventData)
                        swaps = append(swaps, swapData)
                    }
                }
            }
        }
    }
    
    return swaps, createSwaps
}
```

#### 2.2.3 创建事件处理
```go
type PumpfunCreateEvent struct {
    Name         string           // 代币名称
    Symbol       string           // 代币符号
    Uri          string           // 元数据 URI
    Mint         solana.PublicKey // Mint 地址
    BondingCurve solana.PublicKey // 绑定曲线地址
    User         solana.PublicKey // 创建者地址
}
```

### 2.3 Jupiter 聚合器

#### 2.3.1 业务特点
- **路径聚合**: 自动寻找最优交易路径
- **多协议整合**: 整合多个 DEX 的流动性
- **滑点保护**: 提供滑点保护机制
- **MEV 保护**: 防止最大可提取价值攻击

#### 2.3.2 路由事件处理
```go
type JupiterRouteEvent struct {
    AmountIn     uint64           // 输入金额
    AmountOut    uint64           // 输出金额
    TokenIn      solana.PublicKey // 输入代币
    TokenOut     solana.PublicKey // 输出代币
    User         solana.PublicKey // 用户地址
    RouteSteps   []RouteStep      // 路由步骤
}

func (p *Parser) processJupiterSwaps(instructionIndex int) []SwapData {
    var swaps []SwapData
    
    for _, innerInstructionSet := range p.txMeta.InnerInstructions {
        if innerInstructionSet.Index == uint32(instructionIndex) {
            for _, innerInstruction := range innerInstructionSet.Instructions {
                if p.isJupiterRouteEventInstruction(innerInstruction) {
                    eventData, err := p.parseJupiterRouteEventInstruction(innerInstruction)
                    if err != nil {
                        p.Log.Errorf("error processing Jupiter route event: %s", err)
                        continue
                    }
                    
                    if eventData != nil {
                        swapData := convertJupiterToSwapData(eventData)
                        swaps = append(swaps, swapData)
                    }
                }
            }
        }
    }
    
    return swaps
}
```

### 2.4 OKX DEX 路由器

#### 2.4.1 业务特点
- **中心化交易所**: 结合 CEX 和 DEX 的优势
- **深度聚合**: 聚合多个流动性源
- **专业交易**: 面向专业交易者的高级功能
- **跨链支持**: 支持多链资产交易

#### 2.4.2 交易处理逻辑
```go
func (p *Parser) processOKXSwaps(instructionIndex int) []SwapData {
    var swaps []SwapData
    seen := make(map[string]bool)
    processedProtocols := make(map[string]bool)
    
    innerInstructions := p.getInnerInstructions(instructionIndex)
    
    for _, inner := range innerInstructions {
        progID := p.allAccountKeys[inner.ProgramIdIndex]
        
        switch {
        case progID.Equals(RAYDIUM_V4_PROGRAM_ID) ||
             progID.Equals(RAYDIUM_CPMM_PROGRAM_ID):
            if !processedProtocols[RAYDIUM] {
                raydSwaps := p.processRaydSwaps(instructionIndex)
                for _, swap := range raydSwaps {
                    key := getSwapKey(swap)
                    if !seen[key] {
                        swaps = append(swaps, swap)
                        seen[key] = true
                    }
                }
                processedProtocols[RAYDIUM] = true
            }
        }
    }
    
    return swaps
}
```

## 3. 交易类型分类

### 3.1 交易类型定义

#### 3.1.1 基础交易类型
```go
const (
    TX_TYPE_BUY  = "BUY"   // 买入：SOL -> Token
    TX_TYPE_SELL = "SELL"  // 卖出：Token -> SOL
    TX_TYPE_SWAP = "SWAP"  // 交换：Token -> Token
)

func determineTxType(tokenIn, tokenOut string) string {
    solAddress := "So11111111111111111111111111111111111111112"
    
    switch {
    case tokenIn == solAddress && tokenOut != solAddress:
        return TX_TYPE_BUY
    case tokenIn != solAddress && tokenOut == solAddress:
        return TX_TYPE_SELL
    case tokenIn != solAddress && tokenOut != solAddress:
        return TX_TYPE_SWAP
    default:
        return "UNKNOWN"
    }
}
```

#### 3.1.2 扩展交易类型
- **CREATE**: 新代币创建
- **MINT**: 代币铸造
- **BURN**: 代币销毁
- **STAKE**: 质押操作
- **UNSTAKE**: 取消质押
- **CLAIM**: 奖励领取

### 3.2 金额计算逻辑

#### 3.2.1 精度处理
```go
func normalizeAmount(amount uint64, decimals uint32) *big.Float {
    divisor := new(big.Int).Exp(big.NewInt(10), big.NewInt(int64(decimals)), nil)
    amountBig := new(big.Int).SetUint64(amount)
    result := new(big.Float).SetInt(amountBig)
    divisorFloat := new(big.Float).SetInt(divisor)
    return result.Quo(result, divisorFloat)
}
```

#### 3.2.2 价格计算
```go
func calculatePrice(amountIn, amountOut uint64, decimalsIn, decimalsOut uint32) *big.Float {
    normalizedIn := normalizeAmount(amountIn, decimalsIn)
    normalizedOut := normalizeAmount(amountOut, decimalsOut)
    
    if normalizedIn.Cmp(big.NewFloat(0)) == 0 {
        return big.NewFloat(0)
    }
    
    price := new(big.Float).Quo(normalizedOut, normalizedIn)
    return price
}
```

## 4. 用户行为分析

### 4.1 用户分类

#### 4.1.1 交易者类型
- **散户投资者**: 小额、不频繁的交易
- **专业交易者**: 大额、频繁的交易
- **套利机器人**: 高频、自动化交易
- **流动性提供者**: 主要进行流动性操作
- **Meme 币猎手**: 专注于新兴 Meme 代币

#### 4.1.2 行为模式识别
```go
type UserBehaviorPattern struct {
    UserAddr        string
    TxCount         int64
    TotalVolume     *big.Float
    AvgTxSize       *big.Float
    PreferredTokens []string
    TradingHours    []int
    RiskLevel       string
}

func analyzeUserBehavior(userAddr string, transactions []*model.MemeTx) *UserBehaviorPattern {
    pattern := &UserBehaviorPattern{
        UserAddr: userAddr,
        TxCount:  int64(len(transactions)),
    }
    
    // 计算总交易量
    totalVolume := big.NewFloat(0)
    tokenFreq := make(map[string]int)
    hourFreq := make(map[int]int)
    
    for _, tx := range transactions {
        // 累计交易量
        amountIn, _ := new(big.Float).SetString(tx.AmountIn)
        totalVolume.Add(totalVolume, amountIn)
        
        // 统计偏好代币
        tokenFreq[tx.TokenInAddr]++
        tokenFreq[tx.TokenOutAddr]++
        
        // 统计交易时间
        hour := tx.Ts.Hour()
        hourFreq[hour]++
    }
    
    pattern.TotalVolume = totalVolume
    pattern.AvgTxSize = new(big.Float).Quo(totalVolume, big.NewFloat(float64(pattern.TxCount)))
    
    return pattern
}
```

### 4.2 风险评估

#### 4.2.1 风险指标
```go
type RiskMetrics struct {
    SuspiciousActivity bool
    HighFrequency      bool
    LargeVolume        bool
    NewTokenFocus      bool
    MEVActivity        bool
}

func assessRisk(pattern *UserBehaviorPattern) *RiskMetrics {
    metrics := &RiskMetrics{}
    
    // 高频交易检测
    if pattern.TxCount > 1000 {
        metrics.HighFrequency = true
    }
    
    // 大额交易检测
    threshold := big.NewFloat(100000) // $100k
    if pattern.TotalVolume.Cmp(threshold) > 0 {
        metrics.LargeVolume = true
    }
    
    // 新代币关注度检测
    newTokenRatio := calculateNewTokenRatio(pattern.PreferredTokens)
    if newTokenRatio > 0.8 {
        metrics.NewTokenFocus = true
    }
    
    return metrics
}
```

#### 4.2.2 异常检测
```go
func detectAnomalies(transactions []*model.MemeTx) []Anomaly {
    var anomalies []Anomaly
    
    for i, tx := range transactions {
        // 检测异常大额交易
        amountIn, _ := new(big.Float).SetString(tx.AmountIn)
        if amountIn.Cmp(big.NewFloat(1000000)) > 0 { // > $1M
            anomalies = append(anomalies, Anomaly{
                Type:        "LARGE_TRANSACTION",
                TxHash:      tx.TxHash,
                Description: "Transaction amount exceeds $1M",
                Severity:    "HIGH",
            })
        }
        
        // 检测快速连续交易
        if i > 0 {
            timeDiff := tx.Ts.Sub(transactions[i-1].Ts)
            if timeDiff < time.Second {
                anomalies = append(anomalies, Anomaly{
                    Type:        "RAPID_TRADING",
                    TxHash:      tx.TxHash,
                    Description: "Rapid consecutive transactions",
                    Severity:    "MEDIUM",
                })
            }
        }
    }
    
    return anomalies
}
```

## 5. 市场数据分析

### 5.1 代币热度分析

#### 5.1.1 热度指标
```go
type TokenHotness struct {
    TokenAddr       string
    TxCount24h      int64
    Volume24h       *big.Float
    UniqueUsers24h  int64
    PriceChange24h  float64
    HotnessScore    float64
}

func calculateHotness(tokenAddr string, transactions []*model.MemeTx) *TokenHotness {
    now := time.Now()
    cutoff := now.Add(-24 * time.Hour)
    
    hotness := &TokenHotness{
        TokenAddr: tokenAddr,
        Volume24h: big.NewFloat(0),
    }
    
    uniqueUsers := make(map[string]bool)
    
    for _, tx := range transactions {
        if tx.Ts.After(cutoff) {
            hotness.TxCount24h++
            
            // 累计交易量
            if tx.TokenInAddr == tokenAddr {
                amountIn, _ := new(big.Float).SetString(tx.AmountIn)
                hotness.Volume24h.Add(hotness.Volume24h, amountIn)
            }
            if tx.TokenOutAddr == tokenAddr {
                amountOut, _ := new(big.Float).SetString(tx.AmountOut)
                hotness.Volume24h.Add(hotness.Volume24h, amountOut)
            }
            
            // 统计独立用户
            uniqueUsers[tx.UserAddr] = true
        }
    }
    
    hotness.UniqueUsers24h = int64(len(uniqueUsers))
    
    // 计算热度分数
    hotness.HotnessScore = calculateHotnessScore(hotness)
    
    return hotness
}
```

#### 5.1.2 趋势分析
```go
type TrendAnalysis struct {
    TokenAddr     string
    TrendType     string // "RISING", "FALLING", "STABLE"
    Momentum      float64
    VolumeGrowth  float64
    UserGrowth    float64
}

func analyzeTrend(tokenAddr string, historicalData []*TokenHotness) *TrendAnalysis {
    if len(historicalData) < 2 {
        return &TrendAnalysis{
            TokenAddr: tokenAddr,
            TrendType: "INSUFFICIENT_DATA",
        }
    }
    
    latest := historicalData[len(historicalData)-1]
    previous := historicalData[len(historicalData)-2]
    
    analysis := &TrendAnalysis{
        TokenAddr: tokenAddr,
    }
    
    // 计算增长率
    volumeGrowth := calculateGrowthRate(previous.Volume24h, latest.Volume24h)
    userGrowth := float64(latest.UniqueUsers24h-previous.UniqueUsers24h) / float64(previous.UniqueUsers24h)
    
    analysis.VolumeGrowth = volumeGrowth
    analysis.UserGrowth = userGrowth
    
    // 确定趋势类型
    if volumeGrowth > 0.2 && userGrowth > 0.1 {
        analysis.TrendType = "RISING"
        analysis.Momentum = (volumeGrowth + userGrowth) / 2
    } else if volumeGrowth < -0.2 || userGrowth < -0.1 {
        analysis.TrendType = "FALLING"
        analysis.Momentum = -((-volumeGrowth - userGrowth) / 2)
    } else {
        analysis.TrendType = "STABLE"
        analysis.Momentum = 0
    }
    
    return analysis
}
```

### 5.2 流动性分析

#### 5.2.1 流动性指标
```go
type LiquidityMetrics struct {
    TokenAddr       string
    TotalLiquidity  *big.Float
    BidAskSpread    float64
    MarketDepth     *big.Float
    ImpactScore     float64
}
```

#### 5.2.2 价格影响分析
```go
func calculatePriceImpact(tokenAddr string, tradeSize *big.Float, 
    liquidityMetrics *LiquidityMetrics) float64 {
    
    if liquidityMetrics.TotalLiquidity.Cmp(big.NewFloat(0)) == 0 {
        return 1.0 // 100% impact if no liquidity
    }
    
    ratio := new(big.Float).Quo(tradeSize, liquidityMetrics.TotalLiquidity)
    impact, _ := ratio.Float64()
    
    // 应用价格影响公式
    priceImpact := math.Sqrt(impact) * 0.5
    
    return math.Min(priceImpact, 1.0)
}
```
