# Solana 交易处理系统 - 数据流详细文档

## 1. 数据流概述

### 1.1 整体数据流向
```
Solana 区块链 → gRPC 节点 → Chain Quote Server → Kafka → Model Processor → 数据库 → WebSocket 客户端
```

### 1.2 数据流特点
- **实时性**: 毫秒级的数据传输延迟
- **可靠性**: 基于 Kafka 的消息持久化
- **可扩展性**: 支持水平扩展和负载均衡
- **容错性**: 多层次的错误处理和恢复机制

## 2. 数据采集层

### 2.1 gRPC 数据订阅

#### 2.1.1 订阅请求构建
```go
func buildSubscriptionRequest(cfg *config.Config) pb.SubscribeRequest {
    var filters []*pb.SubscribeRequestFilterTransactions
    
    // 构建交易过滤器
    if cfg.Transactions.Enabled {
        filter := &pb.SubscribeRequestFilterTransactions{
            Vote:           &cfg.Transactions.Vote,
            Failed:         &cfg.Transactions.Failed,
            AccountInclude: cfg.Transactions.AccountInclude,
            AccountExclude: cfg.Transactions.AccountExclude,
        }
        filters = append(filters, filter)
    }
    
    return pb.SubscribeRequest{
        Transactions: filters,
        Slots:        &pb.SubscribeRequestFilterSlots{},
        Blocks:       &pb.SubscribeRequestFilterBlocks{},
        BlocksMeta:   &pb.SubscribeRequestFilterBlocksMeta{},
        Accounts:     []*pb.SubscribeRequestFilterAccounts{},
    }
}
```

#### 2.1.2 数据接收流程
1. **建立连接**: 创建 gRPC 客户端连接
2. **发送订阅**: 发送订阅请求到服务器
3. **接收数据**: 持续接收流数据
4. **错误处理**: 处理连接断开和重连

### 2.2 监控地址管理

#### 2.2.1 地址列表维护
- **静态地址**: 配置文件中预定义的 DEX 程序地址
- **动态地址**: 通过 Kafka 消息动态添加的监控地址
- **地址更新**: 支持实时更新监控地址列表

#### 2.2.2 重订阅机制
```go
// 地址更新触发重订阅
func (s *SubscriptionManager) updateAddresses(newAddresses []string) {
    s.mu.Lock()
    s.monitoredAddresses = newAddresses
    s.mu.Unlock()
    
    // 触发重订阅
    s.resubscribeChan <- struct{}{}
}
```

## 3. 消息队列层

### 3.1 Kafka 主题设计

#### 3.1.1 主题结构
```yaml
主题列表:
  - origin-tx: 原始交易数据
  - update-address-topic: 地址更新消息
  - active-address: 活跃地址信息
  - final-tx: 处理后的交易数据
  - addresses: 地址监控消息
  - tokens: 代币监控消息
```

#### 3.1.2 消息格式
```protobuf
// 原始交易消息
message TransactionUpdate {
    Transaction transaction = 1;
    TransactionStatusMeta meta = 2;
    uint64 slot = 3;
}

// 地址更新消息
message AddressUpdate {
    string address = 1;
    string action = 2;  // "add" or "remove"
    int64 timestamp = 3;
}
```

### 3.2 生产者实现

#### 3.2.1 异步生产者
```go
type AsyncTransactionProducer struct {
    writer     *kafkago.Writer
    topic      string
    closeCh    chan struct{}
    wg         sync.WaitGroup
    msgCount   int64
    errCount   int64
    lastReport time.Time
}

func (ap *AsyncTransactionProducer) SendRawBytes(signature string, slot uint64, data []byte) {
    msg := kafkago.Message{
        Key:   []byte(signature),
        Value: data,
        Time:  time.Now(),
    }
    
    err := ap.writer.WriteMessages(context.Background(), msg)
    if err != nil {
        atomic.AddInt64(&ap.errCount, 1)
        log.Printf("Produce error: %v", err)
    } else {
        atomic.AddInt64(&ap.msgCount, 1)
    }
}
```

#### 3.2.2 批量处理优化
- **批量大小**: 100 条消息为一批
- **批量超时**: 100ms 超时自动发送
- **压缩算法**: 使用 Snappy 压缩
- **确认机制**: 要求所有副本确认

### 3.3 消费者实现

#### 3.3.1 多消费者架构
```go
func (c *Client) StartConsumer(ctx context.Context, wg *sync.WaitGroup, 
    cfg config.KafkaConfig, txHandler, addrHandler, tokenHandler func(...) error) error {
    
    // 为输入主题创建多个消费者
    for i := 0; i < c.numConsumers; i++ {
        reader := kafka.NewReader(buildReaderConfig(inputTopic, c.groupID))
        c.readers = append(c.readers, reader)
        
        wg.Add(1)
        go consumeLoop(reader, "input", inputTopic, func(m kafka.Message) {
            if txHandler != nil {
                txHandler(m.Value, int32(m.Partition))
            }
        }, fmt.Sprintf("Worker-%d", i+1))
    }
}
```

#### 3.3.2 消费者配置
- **消费者组**: 支持多实例负载均衡
- **偏移管理**: 自动提交偏移量
- **重试机制**: 消费失败自动重试
- **错误处理**: Panic 恢复和错误日志

## 4. 数据处理层

### 4.1 交易解析流程

#### 4.1.1 Protobuf 解码
```go
func ProcessUpdate(messageValue []byte, partitionID int32) error {
    // 解码 Protobuf 消息
    var update proto.SubscribeUpdate
    if err := proto.Unmarshal(messageValue, &update); err != nil {
        return fmt.Errorf("failed to unmarshal message: %w", err)
    }
    
    // 提取交易信息
    if tx := update.GetTransaction(); tx != nil {
        return s.processTransaction(tx, &update)
    }
    
    return nil
}
```

#### 4.1.2 协议识别逻辑
```go
func (p *Parser) ParseSwaps() (map[int][]SwapData, map[int][]CreateData, error) {
    parsedSwaps := make(map[int][]SwapData)
    parsedCreates := make(map[int][]CreateData)
    
    // 处理外部指令
    for i, outerInstruction := range p.txInfo.Message.Instructions {
        progID := p.allAccountKeys[outerInstruction.ProgramIdIndex]
        
        switch {
        case progID.Equals(JUPITER_PROGRAM_ID):
            parsedSwaps[i] = append(parsedSwaps[i], p.processJupiterSwaps(i)...)
        case progID.Equals(PUMP_FUN_PROGRAM_ID):
            parsedSwaps[i] = append(parsedSwaps[i], p.processPumpfunSwaps(i)...)
        case p.isRaydiumProgram(progID):
            parsedSwaps[i] = append(parsedSwaps[i], p.processRaydSwaps(i)...)
        }
    }
    
    return parsedSwaps, parsedCreates, nil
}
```

### 4.2 数据转换流程

#### 4.2.1 交易数据标准化
```go
func convertToMemeTx(swapData SwapData, txInfo *TransactionInfo) *model.MemeTx {
    return &model.MemeTx{
        Ts:               time.Unix(txInfo.BlockTime, 0),
        BlockTime:        txInfo.BlockTime,
        TxHash:           txInfo.Signature,
        UserAddr:         swapData.UserAddress,
        TokenInAddr:      swapData.TokenIn,
        TokenOutAddr:     swapData.TokenOut,
        AmountIn:         swapData.AmountIn.String(),
        AmountOut:        swapData.AmountOut.String(),
        TxType:           determineTxType(swapData),
        BlockSlot:        int64(txInfo.Slot),
        InstructionIndex: int32(swapData.InstructionIndex),
        TxIndex:          txInfo.TxIndex,
    }
}
```

#### 4.2.2 交易类型判断
```go
func determineTxType(swapData SwapData) string {
    // SOL 地址
    solAddress := "So11111111111111111111111111111111111111112"
    
    if swapData.TokenIn == solAddress {
        return "BUY"  // SOL -> Token
    } else if swapData.TokenOut == solAddress {
        return "SELL" // Token -> SOL
    } else {
        return "SWAP" // Token -> Token
    }
}
```

### 4.3 协议特定处理

#### 4.3.1 Pump.fun 协议处理
```go
func (p *Parser) processPumpfunSwaps(instructionIndex int) ([]SwapData, []CreateData) {
    var swaps []SwapData
    var createSwaps []CreateData
    
    for _, innerInstructionSet := range p.txMeta.InnerInstructions {
        if innerInstructionSet.Index == uint32(instructionIndex) {
            for _, innerInstruction := range innerInstructionSet.Instructions {
                if p.isPumpFunTradeEventInstruction(innerInstruction) {
                    eventData, err := p.parsePumpfunTradeEventInstruction(innerInstruction)
                    if err != nil {
                        p.Log.Errorf("error processing Pumpfun trade event: %s", err)
                        continue
                    }
                    
                    if eventData != nil {
                        swapData := convertPumpfunEventToSwapData(eventData)
                        swaps = append(swaps, swapData)
                    }
                }
            }
        }
    }
    
    return swaps, createSwaps
}
```

#### 4.3.2 Raydium 协议处理
```go
func (p *Parser) processRaydSwaps(instructionIndex int) []SwapData {
    var swaps []SwapData
    
    for _, innerInstructionSet := range p.txMeta.InnerInstructions {
        if innerInstructionSet.Index == uint32(instructionIndex) {
            for _, innerInstruction := range innerInstructionSet.Instructions {
                switch {
                case p.isTransfer(innerInstruction):
                    transfer := p.processTransfer(innerInstruction)
                    if transfer != nil {
                        swaps = append(swaps, SwapData{
                            Type: RAYDIUM, 
                            InstructionIndex: instructionIndex, 
                            Data: transfer
                        })
                    }
                case p.isTransferCheck(innerInstruction):
                    transfer := p.processTransferCheck(innerInstruction)
                    if transfer != nil {
                        swaps = append(swaps, SwapData{
                            Type: RAYDIUM, 
                            InstructionIndex: instructionIndex, 
                            Data: transfer
                        })
                    }
                }
            }
        }
    }
    
    return swaps
}
```

## 5. 数据存储层

### 5.1 数据库设计

#### 5.1.1 TDengine 表结构
```sql
-- 按用户分表的稳定表
CREATE STABLE IF NOT EXISTS solana_transaction_user (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  user_addr     VARCHAR(128)
);

-- 按代币分表的稳定表
CREATE STABLE IF NOT EXISTS solana_transaction_token (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  user_addr     VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  token_addr    VARCHAR(128)
);
```

#### 5.1.2 分表策略
- **按代币分表**: 每个代币创建独立的子表
- **按用户分表**: 每个用户创建独立的子表
- **时间分区**: 基于时间戳的自动分区

### 5.2 数据写入流程

#### 5.2.1 批量写入实现
```go
func (db *TDengineDB) InsertMemeTxBatch(txs []*model.MemeTxWithMeta) error {
    if len(txs) == 0 {
        return nil
    }
    
    // 按代币地址分组
    tokenGroups := make(map[string][]*model.MemeTxWithMeta)
    for _, tx := range txs {
        tokenGroups[tx.TokenAddr] = append(tokenGroups[tx.TokenAddr], tx)
    }
    
    // 为每个代币批量插入
    for tokenAddr, tokenTxs := range tokenGroups {
        if err := db.insertTokenBatch(tokenAddr, tokenTxs); err != nil {
            return fmt.Errorf("failed to insert batch for token %s: %w", tokenAddr, err)
        }
    }
    
    return nil
}
```

#### 5.2.2 子表管理
```go
func (db *TDengineDB) ensureChildTable(mintAddr, network string) error {
    tableName := fmt.Sprintf("tx_%s", mintAddr)
    
    query := fmt.Sprintf(`
        CREATE TABLE IF NOT EXISTS %s USING solana_transaction_token 
        TAGS ('%s')
    `, tableName, mintAddr)
    
    _, err := db.conn.Exec(query)
    return err
}
```

### 5.3 查询优化

#### 5.3.1 索引策略
- **时间索引**: 基于时间戳的主索引
- **标签索引**: 基于代币地址和用户地址的标签索引
- **复合索引**: 多字段组合索引

#### 5.3.2 查询模式
```sql
-- 按代币查询最近交易
SELECT * FROM tx_DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 
WHERE ts >= NOW() - INTERVAL(1h) 
ORDER BY ts DESC LIMIT 100;

-- 按用户查询交易历史
SELECT * FROM solana_transaction_user 
WHERE user_addr = 'USER_ADDRESS' 
AND ts >= NOW() - INTERVAL(24h);
```

## 6. 实时推送层

### 6.1 WebSocket 服务器

#### 6.1.1 连接管理
```go
type WebSocketServer struct {
    clients     map[*websocket.Conn]*Client
    clientsMux  sync.RWMutex
    upgrader    websocket.Upgrader
    broadcast   chan []byte
}

type Client struct {
    conn          *websocket.Conn
    subscriptions map[string]bool
    send          chan []byte
}
```

#### 6.1.2 订阅管理
```go
func (ws *WebSocketServer) handleSubscription(client *Client, msg SubscriptionMessage) {
    client.subscriptionsMux.Lock()
    defer client.subscriptionsMux.Unlock()
    
    switch msg.Action {
    case "subscribe":
        client.subscriptions[msg.Topic] = true
        log.Printf("Client subscribed to topic: %s", msg.Topic)
    case "unsubscribe":
        delete(client.subscriptions, msg.Topic)
        log.Printf("Client unsubscribed from topic: %s", msg.Topic)
    }
}
```

### 6.2 消息推送机制

#### 6.2.1 推送触发
```go
func (s *TransactionService) onTransactionProcessed(tx *model.MemeTx, mintAddr, network string) {
    if s.wsServer != nil {
        message := TransactionMessage{
            MintAddr:     mintAddr,
            TxHash:       tx.TxHash,
            UserAddr:     tx.UserAddr,
            TokenInAddr:  tx.TokenInAddr,
            TokenOutAddr: tx.TokenOutAddr,
            AmountIn:     parseFloat(tx.AmountIn),
            AmountOut:    parseFloat(tx.AmountOut),
            TxType:       tx.TxType,
            BlockSlot:    tx.BlockSlot,
            Timestamp:    tx.Ts.Format(time.RFC3339),
            Network:      network,
        }
        
        s.wsServer.BroadcastTransaction(message)
    }
}
```

#### 6.2.2 消息路由
```go
func (ws *WebSocketServer) BroadcastTransaction(tx TransactionMessage) {
    ws.clientsMux.RLock()
    defer ws.clientsMux.RUnlock()
    
    for _, client := range ws.clients {
        client.subscriptionsMux.RLock()
        shouldSend := false
        
        // 检查订阅条件
        if client.subscriptions[tx.MintAddr] ||
           client.subscriptions["type:"+tx.TxType] ||
           client.subscriptions["network:"+tx.Network] {
            shouldSend = true
        }
        
        client.subscriptionsMux.RUnlock()
        
        if shouldSend {
            select {
            case client.send <- messageBytes:
                // 消息发送成功
            default:
                // 客户端缓冲区满，关闭连接
                close(client.send)
                delete(ws.clients, client.conn)
            }
        }
    }
}
```

## 7. 监控和统计

### 7.1 性能指标收集

#### 7.1.1 处理统计
```go
type ProcessingStats struct {
    TotalProcessed    int64
    SuccessfulWrites  int64
    FailedWrites      int64
    ParseErrors       int64
    ProcessingTime    time.Duration
    LastProcessedTime time.Time
}

func (s *StatsService) UpdateStats(processed, successful, failed, errors int64, duration time.Duration) {
    atomic.AddInt64(&s.stats.TotalProcessed, processed)
    atomic.AddInt64(&s.stats.SuccessfulWrites, successful)
    atomic.AddInt64(&s.stats.FailedWrites, failed)
    atomic.AddInt64(&s.stats.ParseErrors, errors)
    s.stats.ProcessingTime = duration
    s.stats.LastProcessedTime = time.Now()
}
```

#### 7.1.2 定期报告
```go
func (s *StatsService) StartCollector(ctx context.Context, interval time.Duration, tokenService *TokenService) {
    ticker := time.NewTicker(interval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ctx.Done():
            return
        case <-ticker.C:
            s.collectAndReport(tokenService)
        }
    }
}
```

### 7.2 业务指标统计

#### 7.2.1 协议分布统计
```go
type ProtocolStats struct {
    Raydium   int64
    Orca      int64
    Meteora   int64
    PumpFun   int64
    Jupiter   int64
    OKX       int64
    Others    int64
}
```

#### 7.2.2 代币热度统计
```go
type TokenStats struct {
    TokenAddr     string
    TxCount       int64
    Volume        *big.Int
    UniqueUsers   int64
    LastActivity  time.Time
}
```
