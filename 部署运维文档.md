# Solana 交易处理系统 - 部署运维文档

## 1. 系统要求

### 1.1 硬件要求

#### 1.1.1 最小配置
- **CPU**: 4 核心 2.0GHz
- **内存**: 8GB RAM
- **存储**: 100GB SSD
- **网络**: 100Mbps 带宽

#### 1.1.2 推荐配置
- **CPU**: 8 核心 3.0GHz
- **内存**: 16GB RAM
- **存储**: 500GB NVMe SSD
- **网络**: 1Gbps 带宽

#### 1.1.3 生产环境配置
- **CPU**: 16 核心 3.5GHz
- **内存**: 32GB RAM
- **存储**: 1TB NVMe SSD
- **网络**: 10Gbps 带宽

### 1.2 软件要求

#### 1.2.1 基础环境
```bash
# 操作系统
Ubuntu 20.04 LTS 或更高版本
CentOS 8 或更高版本

# Go 环境
Go 1.21 或更高版本

# 数据库
TDengine 3.0+ 或 InfluxDB 2.0+

# 消息队列
Apache Kafka 2.8+ 或 AWS MSK
```

#### 1.2.2 依赖服务
- **Zookeeper**: Kafka 集群管理
- **Prometheus**: 监控指标收集
- **Grafana**: 监控数据可视化
- **Nginx**: 反向代理和负载均衡

## 2. 环境准备

### 2.1 Go 环境安装

```bash
# 下载并安装 Go
wget https://go.dev/dl/go1.21.0.linux-amd64.tar.gz
sudo tar -C /usr/local -xzf go1.21.0.linux-amd64.tar.gz

# 设置环境变量
echo 'export PATH=$PATH:/usr/local/go/bin' >> ~/.bashrc
echo 'export GOPATH=$HOME/go' >> ~/.bashrc
echo 'export GOBIN=$GOPATH/bin' >> ~/.bashrc
source ~/.bashrc

# 验证安装
go version
```

### 2.2 Kafka 集群部署

#### 2.2.1 单机部署
```bash
# 下载 Kafka
wget https://downloads.apache.org/kafka/2.8.0/kafka_2.13-2.8.0.tgz
tar -xzf kafka_2.13-2.8.0.tgz
cd kafka_2.13-2.8.0

# 启动 Zookeeper
bin/zookeeper-server-start.sh config/zookeeper.properties &

# 启动 Kafka
bin/kafka-server-start.sh config/server.properties &

# 创建主题
bin/kafka-topics.sh --create --topic origin-tx --bootstrap-server localhost:9092 --partitions 3 --replication-factor 1
bin/kafka-topics.sh --create --topic update-address-topic --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1
bin/kafka-topics.sh --create --topic active-address --bootstrap-server localhost:9092 --partitions 1 --replication-factor 1
```

#### 2.2.2 集群部署
```bash
# server-1.properties
broker.id=1
listeners=PLAINTEXT://kafka1:9092
log.dirs=/var/kafka-logs-1
zookeeper.connect=zk1:2181,zk2:2181,zk3:2181

# server-2.properties
broker.id=2
listeners=PLAINTEXT://kafka2:9092
log.dirs=/var/kafka-logs-2
zookeeper.connect=zk1:2181,zk2:2181,zk3:2181

# server-3.properties
broker.id=3
listeners=PLAINTEXT://kafka3:9092
log.dirs=/var/kafka-logs-3
zookeeper.connect=zk1:2181,zk2:2181,zk3:2181
```

### 2.3 TDengine 数据库部署

#### 2.3.1 安装 TDengine
```bash
# 下载安装包
wget https://www.taosdata.com/assets-download/3.0/TDengine-server-*******-Linux-x64.tar.gz
tar -xzf TDengine-server-*******-Linux-x64.tar.gz
cd TDengine-server-*******

# 安装
sudo ./install.sh

# 启动服务
sudo systemctl start taosd
sudo systemctl enable taosd
```

#### 2.3.2 初始化数据库
```sql
-- 连接到 TDengine
taos

-- 创建数据库
CREATE DATABASE solana_data KEEP 7d DURATION 1d PRECISION 'us';

-- 使用数据库
USE solana_data;

-- 创建稳定表
CREATE STABLE IF NOT EXISTS solana_transaction_user (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  user_addr     VARCHAR(128)
);

CREATE STABLE IF NOT EXISTS solana_transaction_token (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  user_addr     VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  token_addr    VARCHAR(128)
);

CREATE STABLE IF NOT EXISTS processed_txhash (
  ts            TIMESTAMP,
  tx_hash       VARCHAR(128),
  block_time    BIGINT,
  network       VARCHAR(16),
  status        VARCHAR(16),
  process_time  TIMESTAMP
) TAGS (
  block_slot    BIGINT
);
```

## 3. 应用部署

### 3.1 编译构建

#### 3.1.1 Chain Quote Server
```bash
cd chain-quote-server

# 安装依赖
go mod tidy

# 构建应用
make build

# 或者交叉编译
make linux        # Linux x86_64
make darwin       # macOS
make windows      # Windows
```

#### 3.1.2 Model Processor
```bash
cd model-processor

# 安装依赖
go mod tidy

# 构建应用
make build

# 或者指定平台构建
make build-linux
```

### 3.2 配置文件

#### 3.2.1 Chain Quote Server 配置
```toml
# config.toml
[grpc]
endpoint = "http://your-grpc-node:10000"
token = "your-access-token"
insecure = false

[transactions]
enabled = true
vote = false
failed = false
account_include = [
    "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
    "DCAK36VfExkPdAkYUQg6ewgxyinvcEyPLyHjRbmveKFw",
    "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P",
]

[kafka]
brokers = ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
use_msk = false
topic = "origin-tx"
consumer_topic = "update-address-topic"
active_address_topic = "active-address"

[worker_pool]
size = 10
```

#### 3.2.2 Model Processor 配置
```yaml
# config.yaml
kafka:
  brokers: ["kafka1:9092", "kafka2:9092", "kafka3:9092"]
  input_topic: "origin-tx"
  output_topic: "final-tx"
  addresses_topic: "addresses"
  tokens_topic: "tokens"
  group_id: "model-processor-group-1"
  num_consumers: 3

database:
  type: "tdengine"

tdengine:
  host: "tdengine-server"
  port: "6041"
  user: "root"
  password: "taosdata"
  database: "solana_data"

websocket:
  enabled: true
  host: "0.0.0.0"
  port: "8080"
  read_buffer_size: 1024
  write_buffer_size: 1024
  check_origin: false

log:
  level: "info"
```

### 3.3 服务启动

#### 3.3.1 使用 systemd 管理服务

**Chain Quote Server 服务文件**
```ini
# /etc/systemd/system/chain-quote-server.service
[Unit]
Description=Chain Quote Server
After=network.target

[Service]
Type=simple
User=solana
WorkingDirectory=/opt/solana/chain-quote-server
ExecStart=/opt/solana/chain-quote-server/bin/grpc-client
Restart=always
RestartSec=5
Environment=GOMAXPROCS=4

[Install]
WantedBy=multi-user.target
```

**Model Processor 服务文件**
```ini
# /etc/systemd/system/model-processor.service
[Unit]
Description=Model Processor
After=network.target

[Service]
Type=simple
User=solana
WorkingDirectory=/opt/solana/model-processor
ExecStart=/opt/solana/model-processor/model-processor
Restart=always
RestartSec=5
Environment=GOMAXPROCS=8

[Install]
WantedBy=multi-user.target
```

#### 3.3.2 启动服务
```bash
# 重新加载 systemd 配置
sudo systemctl daemon-reload

# 启动服务
sudo systemctl start chain-quote-server
sudo systemctl start model-processor

# 设置开机自启
sudo systemctl enable chain-quote-server
sudo systemctl enable model-processor

# 查看服务状态
sudo systemctl status chain-quote-server
sudo systemctl status model-processor
```

## 4. Docker 部署

### 4.1 Dockerfile

#### 4.1.1 Chain Quote Server Dockerfile
```dockerfile
# Dockerfile.chain-quote-server
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o grpc-client ./cmd/grpc-client

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/grpc-client .
COPY --from=builder /app/config.toml .

CMD ["./grpc-client"]
```

#### 4.1.2 Model Processor Dockerfile
```dockerfile
# Dockerfile.model-processor
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=0 GOOS=linux go build -o model-processor .

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/

COPY --from=builder /app/model-processor .
COPY --from=builder /app/config.yaml .

CMD ["./model-processor"]
```

### 4.2 Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  tdengine:
    image: tdengine/tdengine:*******
    ports:
      - "6030:6030"
      - "6041:6041"
    environment:
      TAOS_FQDN: tdengine
    volumes:
      - tdengine-data:/var/lib/taos

  chain-quote-server:
    build:
      context: ./chain-quote-server
      dockerfile: Dockerfile.chain-quote-server
    depends_on:
      - kafka
    environment:
      - KAFKA_BROKERS=kafka:9092
    volumes:
      - ./chain-quote-server/config.toml:/root/config.toml

  model-processor:
    build:
      context: ./model-processor
      dockerfile: Dockerfile.model-processor
    depends_on:
      - kafka
      - tdengine
    ports:
      - "8080:8080"
    environment:
      - KAFKA_BROKERS=kafka:9092
      - TDENGINE_HOST=tdengine
    volumes:
      - ./model-processor/config.yaml:/root/config.yaml

volumes:
  tdengine-data:
```

### 4.3 启动 Docker 环境

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f chain-quote-server
docker-compose logs -f model-processor

# 停止服务
docker-compose down
```

## 5. 监控配置

### 5.1 Prometheus 配置

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'chain-quote-server'
    static_configs:
      - targets: ['localhost:8081']

  - job_name: 'model-processor'
    static_configs:
      - targets: ['localhost:8082']

  - job_name: 'kafka'
    static_configs:
      - targets: ['localhost:9308']

  - job_name: 'tdengine'
    static_configs:
      - targets: ['localhost:6041']
```

### 5.2 Grafana 仪表板

#### 5.2.1 系统指标面板
- CPU 使用率
- 内存使用率
- 磁盘 I/O
- 网络流量

#### 5.2.2 业务指标面板
- 交易处理速度
- 错误率统计
- 协议分布
- 热门代币排行

### 5.3 告警规则

```yaml
# alert.rules.yml
groups:
  - name: solana-system
    rules:
      - alert: HighErrorRate
        expr: rate(errors_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
```

## 6. 日志管理

### 6.1 日志配置

#### 6.1.1 应用日志配置
```go
// 日志配置示例
log.SetFormatter(&logrus.JSONFormatter{
    TimestampFormat: "2006-01-02T15:04:05.000Z",
})
log.SetLevel(logrus.InfoLevel)
log.SetOutput(os.Stdout)
```

#### 6.1.2 日志轮转配置
```bash
# /etc/logrotate.d/solana-services
/var/log/solana/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 0644 solana solana
    postrotate
        systemctl reload chain-quote-server
        systemctl reload model-processor
    endscript
}
```

### 6.2 ELK Stack 集成

#### 6.2.1 Filebeat 配置
```yaml
# filebeat.yml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/solana/*.log
  fields:
    service: solana-system
  fields_under_root: true

output.elasticsearch:
  hosts: ["elasticsearch:9200"]

setup.kibana:
  host: "kibana:5601"
```

#### 6.2.2 Logstash 配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [service] == "solana-system" {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "solana-logs-%{+YYYY.MM.dd}"
  }
}
```

## 7. 备份和恢复

### 7.1 数据备份策略

#### 7.1.1 TDengine 备份
```bash
#!/bin/bash
# backup-tdengine.sh

BACKUP_DIR="/backup/tdengine/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 导出数据
taos -s "SELECT * FROM solana_data.solana_transaction_token" > $BACKUP_DIR/token_transactions.sql
taos -s "SELECT * FROM solana_data.solana_transaction_user" > $BACKUP_DIR/user_transactions.sql

# 压缩备份文件
tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR

# 清理旧备份（保留30天）
find /backup/tdengine -name "*.tar.gz" -mtime +30 -delete
```

#### 7.1.2 配置文件备份
```bash
#!/bin/bash
# backup-config.sh

BACKUP_DIR="/backup/config/$(date +%Y%m%d)"
mkdir -p $BACKUP_DIR

# 备份配置文件
cp /opt/solana/chain-quote-server/config.toml $BACKUP_DIR/
cp /opt/solana/model-processor/config.yaml $BACKUP_DIR/

# 备份系统服务文件
cp /etc/systemd/system/chain-quote-server.service $BACKUP_DIR/
cp /etc/systemd/system/model-processor.service $BACKUP_DIR/

tar -czf $BACKUP_DIR.tar.gz $BACKUP_DIR
rm -rf $BACKUP_DIR
```

### 7.2 灾难恢复

#### 7.2.1 数据恢复流程
1. 停止所有服务
2. 恢复数据库数据
3. 恢复配置文件
4. 重启服务
5. 验证数据完整性

#### 7.2.2 恢复脚本
```bash
#!/bin/bash
# restore.sh

BACKUP_DATE=$1
if [ -z "$BACKUP_DATE" ]; then
    echo "Usage: $0 <backup_date>"
    exit 1
fi

# 停止服务
sudo systemctl stop chain-quote-server
sudo systemctl stop model-processor

# 恢复数据
tar -xzf /backup/tdengine/$BACKUP_DATE.tar.gz -C /tmp/
# 执行数据恢复操作...

# 恢复配置
tar -xzf /backup/config/$BACKUP_DATE.tar.gz -C /tmp/
cp /tmp/$BACKUP_DATE/*.toml /opt/solana/chain-quote-server/
cp /tmp/$BACKUP_DATE/*.yaml /opt/solana/model-processor/

# 重启服务
sudo systemctl start chain-quote-server
sudo systemctl start model-processor

echo "Recovery completed"
```
