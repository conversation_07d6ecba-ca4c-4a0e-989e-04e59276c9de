# Solana 区块链交易监控与处理系统 - 详细架构文档

## 1. 项目概述

### 1.1 系统简介
本项目是一个高性能的 Solana 区块链交易监控与处理系统，采用微服务架构设计，专门用于实时监控、解析和处理 Solana 网络上的 DEX 交易数据。系统支持多种主流 DEX 协议，提供实时数据流处理、WebSocket 推送和时序数据存储功能。

### 1.2 核心功能
- **实时交易监控**: 通过 gRPC 订阅 Solana 网络交易事件
- **多协议解析**: 支持 Raydium、Orca、Meteora、Pump.fun、Jupiter、OKX 等主流 DEX 协议
- **高性能处理**: 基于 Kafka 的异步消息处理架构
- **时序数据存储**: 支持 TDengine 和 InfluxDB 双数据库后端
- **实时数据推送**: WebSocket 服务器提供实时交易数据推送
- **地址监控**: 动态地址监控和余额变动追踪
- **统计监控**: 详细的性能指标和业务统计

### 1.3 技术栈
- **开发语言**: Go 1.21+
- **消息队列**: Apache Kafka / AWS MSK Serverless
- **数据库**: TDengine v3+ / InfluxDB
- **通信协议**: gRPC, WebSocket, Protobuf
- **配置管理**: Viper (支持 YAML 和环境变量)
- **区块链交互**: Solana Go SDK

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "外部系统"
        A[Solana 区块链网络]
        B[gRPC 节点服务]
    end
    
    subgraph "Chain Quote Server"
        C[gRPC 客户端]
        D[交易监听服务]
        E[余额监听服务]
        F[工作池]
    end
    
    subgraph "消息队列"
        G[Kafka Cluster]
        H[origin-tx Topic]
        I[update-address-topic]
        J[active-address Topic]
    end
    
    subgraph "Model Processor"
        K[Kafka 消费者]
        L[交易解析器]
        M[协议处理器]
        N[数据转换器]
    end
    
    subgraph "存储层"
        O[TDengine]
        P[InfluxDB]
    end
    
    subgraph "实时推送"
        Q[WebSocket 服务器]
        R[客户端连接管理]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    D --> F
    F --> H
    E --> I
    H --> K
    I --> K
    K --> L
    L --> M
    M --> N
    N --> O
    N --> P
    N --> Q
    Q --> R
```

### 2.2 服务组件详解

#### 2.2.1 Chain Quote Server (交易监控服务)
**主要职责**: 连接 Solana 网络，监控交易和地址变动

**核心组件**:
- **gRPC 客户端**: 建立与区块链节点的连接
- **交易监听服务**: 订阅和处理交易事件
- **余额监听服务**: 监控地址余额变动
- **工作池**: 并发处理交易数据

**关键特性**:
- 支持 TLS 安全连接
- 指数退避重连机制
- 动态地址订阅管理
- 高并发工作池处理

#### 2.2.2 Model Processor (数据处理服务)
**主要职责**: 解析交易数据，存储到数据库，提供实时推送

**核心组件**:
- **Kafka 消费者**: 多线程消费交易数据
- **交易解析器**: 解析不同协议的交易指令
- **数据库适配器**: 支持多种数据库后端
- **WebSocket 服务器**: 实时数据推送

**关键特性**:
- 多协议交易解析
- 双数据库后端支持
- 实时 WebSocket 推送
- 详细统计监控

## 3. 数据流架构

### 3.1 数据流向图

```mermaid
sequenceDiagram
    participant S as Solana Network
    participant G as gRPC Node
    participant C as Chain Quote Server
    participant K as Kafka
    participant M as Model Processor
    participant D as Database
    participant W as WebSocket Clients

    S->>G: 区块链交易事件
    G->>C: gRPC 流数据
    C->>C: 交易数据解析
    C->>K: 发送到 origin-tx
    K->>M: 消费交易消息
    M->>M: 协议解析处理
    M->>D: 存储交易数据
    M->>W: 实时推送交易
    
    Note over C,K: 地址更新流程
    K->>C: update-address-topic
    C->>C: 更新监控地址列表
    C->>G: 重新订阅
```

### 3.2 数据处理流程

#### 3.2.1 交易监听流程
1. **连接建立**: gRPC 客户端连接到区块链节点
2. **订阅配置**: 根据配置文件构建订阅请求
3. **数据接收**: 持续接收交易流数据
4. **并发处理**: 工作池并发处理交易数据
5. **消息发送**: 将处理后的数据发送到 Kafka

#### 3.2.2 数据解析流程
1. **消息消费**: 从 Kafka 消费 Protobuf 编码的交易数据
2. **协议识别**: 根据程序 ID 识别交易协议
3. **指令解析**: 解析交易指令和内部指令
4. **数据提取**: 提取代币信息、金额、用户地址等
5. **格式转换**: 转换为标准化的数据模型

#### 3.2.3 存储和推送流程
1. **数据验证**: 验证解析后的数据完整性
2. **数据库写入**: 写入时序数据库（按代币分表）
3. **实时推送**: 通过 WebSocket 推送给订阅客户端
4. **统计更新**: 更新处理统计和性能指标

## 4. 核心业务逻辑

### 4.1 支持的 DEX 协议

#### 4.1.1 协议列表
- **Raydium**: AMM 和 CPMM 协议
- **Orca**: Whirlpool 协议
- **Meteora**: 动态 AMM 协议
- **Pump.fun**: Meme 代币发行协议
- **Jupiter**: 聚合交易协议
- **OKX DEX**: 中心化交易所 DEX
- **Moonshot**: 新兴 DEX 协议

#### 4.1.2 解析策略
```go
// 协议识别和处理
switch {
case progID.Equals(JUPITER_PROGRAM_ID):
    swaps = append(swaps, p.processJupiterSwaps(i)...)
case progID.Equals(PUMP_FUN_PROGRAM_ID):
    swaps = append(swaps, p.processPumpfunSwaps(i)...)
case p.isRaydiumProgram(progID):
    swaps = append(swaps, p.processRaydSwaps(i)...)
}
```

### 4.2 交易数据模型

#### 4.2.1 核心数据结构
```go
type MemeTx struct {
    Ts               time.Time `json:"ts"`
    BlockTime        int64     `json:"block_time"`
    TxHash           string    `json:"tx_hash"`
    UserAddr         string    `json:"user_addr"`
    TokenInAddr      string    `json:"token_in_addr"`
    TokenOutAddr     string    `json:"token_out_addr"`
    AmountIn         string    `json:"amount_in"`
    AmountOut        string    `json:"amount_out"`
    TxType           string    `json:"tx_type"`
    BlockSlot        int64     `json:"block_slot"`
    InstructionIndex int32     `json:"instruction_index"`
    TxIndex          int64     `json:"tx_index"`
}
```

#### 4.2.2 数据库表结构
- **按用户分表**: `solana_transaction_user` (以 user_addr 为标签)
- **按代币分表**: `solana_transaction_token` (以 token_addr 为标签)
- **处理记录表**: `processed_txhash` (防重复处理)

### 4.3 实时推送机制

#### 4.3.1 WebSocket 订阅类型
```json
// 代币订阅
{"action": "subscribe", "topic": "TOKEN_MINT_ADDRESS"}

// 交易类型订阅  
{"action": "subscribe", "topic": "type:BUY"}

// 网络订阅
{"action": "subscribe", "topic": "network:solana"}
```

#### 4.3.2 推送消息格式
```json
{
  "mint_addr": "TOKEN_MINT_ADDRESS",
  "tx_hash": "TRANSACTION_HASH", 
  "user_addr": "USER_ADDRESS",
  "token_in_addr": "TOKEN_IN_ADDRESS",
  "token_out_addr": "TOKEN_OUT_ADDRESS",
  "amount_in": 123.45,
  "amount_out": 678.90,
  "tx_type": "BUY",
  "block_slot": *********,
  "timestamp": "2023-04-26T12:34:56Z",
  "network": "solana"
}
```

## 5. 配置管理

### 5.1 Chain Quote Server 配置
```toml
[grpc]
endpoint = "http://127.0.0.1:10000"
token = ""
insecure = false

[transactions]
enabled = true
account_include = [
    "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4",
    # 其他 DEX 程序地址
]

[kafka]
brokers = ["127.0.0.1:9092"]
topic = "origin-tx"
consumer_topic = "update-address-topic"
active_address_topic = "active-address"

[worker_pool]
size = 10
```

### 5.2 Model Processor 配置
```yaml
kafka:
  brokers: ["localhost:9092"]
  input_topic: "origin-tx"
  output_topic: "final-tx"
  group_id: "model-processor-group-1"

database:
  type: "tdengine"  # 或 "influxdb"

tdengine:
  host: "127.0.0.1"
  port: "6041"
  database: "solana_data"

websocket:
  enabled: true
  host: "0.0.0.0"
  port: "8080"
```

## 6. 性能优化

### 6.1 并发处理优化
- **工作池模式**: 可配置的工作协程数量
- **批量处理**: Kafka 批量消费和写入
- **异步处理**: 非阻塞的消息处理流程

### 6.2 存储优化
- **分表策略**: 按代币地址和用户地址分表
- **时序优化**: 基于时间戳的数据分区
- **索引优化**: 针对查询模式的索引设计

### 6.3 网络优化
- **连接复用**: gRPC 连接保活和复用
- **压缩传输**: Kafka 消息压缩
- **批量传输**: WebSocket 消息批量推送

## 7. 监控和运维

### 7.1 性能指标
- **处理速度**: 每秒处理交易数量
- **成功率**: 数据库写入成功率  
- **错误率**: 解析错误和处理错误统计
- **延迟指标**: 端到端处理时间

### 7.2 业务指标
- **交易统计**: 按协议分类的交易量
- **用户活跃度**: 活跃钱包地址数量
- **代币热度**: 热门代币交易统计
- **网络状态**: 区块高度和同步状态

### 7.3 告警机制
- **连接异常**: gRPC 连接断开告警
- **处理延迟**: 消息处理延迟告警
- **错误率**: 错误率超阈值告警
- **存储异常**: 数据库写入失败告警

## 8. 扩展性设计

### 8.1 水平扩展
- **Kafka 分区**: 支持多分区并行处理
- **服务实例**: 支持多实例部署
- **数据库分片**: 按时间和标签分片存储

### 8.2 协议扩展
- **插件化设计**: 新协议解析器可独立开发
- **配置驱动**: 通过配置启用/禁用特定协议
- **版本兼容**: 向后兼容的协议升级机制

### 8.3 功能扩展
- **多链支持**: 架构支持扩展到其他区块链
- **数据源扩展**: 支持多种数据源接入
- **输出格式扩展**: 支持多种数据输出格式

## 9. 详细技术实现

### 9.1 gRPC 连接管理

#### 9.1.1 连接建立
```go
func Connect(address string, plaintext bool) (*grpc.ClientConn, error) {
    var opts []grpc.DialOption

    // 配置 TLS
    if plaintext {
        opts = append(opts, grpc.WithTransportCredentials(insecure.NewCredentials()))
    } else {
        pool, _ := x509.SystemCertPool()
        creds := credentials.NewClientTLSFromCert(pool, "")
        opts = append(opts, grpc.WithTransportCredentials(creds))
    }

    // 添加保活参数
    opts = append(opts, grpc.WithKeepaliveParams(models.GRPCKeepaliveParams))

    return grpc.Dial(address, opts...)
}
```

#### 9.1.2 订阅管理
- **动态订阅**: 根据监控地址列表动态构建订阅请求
- **重连机制**: 连接断开时自动重连
- **流管理**: 管理 gRPC 流的生命周期

### 9.2 工作池实现

#### 9.2.1 工作池结构
```go
type WorkerPool struct {
    workers    int
    tasks      chan *pb.SubscribeUpdate
    wg         sync.WaitGroup
    ctx        context.Context
    cancel     context.CancelFunc
    txProducer *kafka.AsyncTransactionProducer
}
```

#### 9.2.2 并发控制
- **任务队列**: 缓冲通道实现任务队列
- **工作协程**: 可配置数量的工作协程
- **优雅关闭**: 支持优雅关闭和资源清理

### 9.3 Kafka 集成

#### 9.3.1 生产者配置
```go
writer := &kafkago.Writer{
    Addr:         kafkago.TCP(brokers...),
    Topic:        topic,
    Balancer:     &kafkago.Hash{},
    BatchSize:    100,
    BatchTimeout: 100 * time.Millisecond,
    Async:        true,
    RequiredAcks: kafkago.RequireAll,
    Compression:  kafkago.Snappy,
}
```

#### 9.3.2 消费者配置
- **多消费者**: 支持多个消费者并行处理
- **分区策略**: 基于哈希的分区策略
- **偏移管理**: 自动提交偏移量

### 9.4 交易解析引擎

#### 9.4.1 解析器架构
```go
type Parser struct {
    txMeta          *proto.TransactionStatusMeta
    txIndex         uint64
    txInfo          *proto.Transaction
    allAccountKeys  solana.PublicKeySlice
    splTokenInfoMap map[string]TokenInfo
    splDecimalsMap  map[string]uint32
    Log             *logrus.Logger
}
```

#### 9.4.2 协议处理流程
1. **程序识别**: 根据程序 ID 识别协议类型
2. **指令解析**: 解析外部指令和内部指令
3. **数据提取**: 提取交易相关的所有数据
4. **格式转换**: 转换为统一的数据格式

### 9.5 数据库适配器

#### 9.5.1 接口设计
```go
type Database interface {
    InsertMemeTx(tx *MemeTx, mintAddr string, network string) error
    InsertMemeTxBatch(txs []*MemeTxWithMeta) error
    GetMemeTxByHash(txHash string) (*MemeTx, error)
    DeleteMemeTxByHash(txHash string) (int64, error)
    HealthCheck() error
    GetType() string
}
```

#### 9.5.2 TDengine 实现
- **分表策略**: 按代币地址创建子表
- **批量插入**: 支持批量数据插入
- **时序优化**: 基于时间戳的数据组织

### 9.6 WebSocket 服务器

#### 9.6.1 连接管理
- **连接池**: 管理所有活跃的 WebSocket 连接
- **订阅管理**: 维护客户端订阅关系
- **消息路由**: 根据订阅关系路由消息

#### 9.6.2 消息处理
```go
type SubscriptionMessage struct {
    Action string `json:"action"` // subscribe/unsubscribe
    Topic  string `json:"topic"`  // 订阅主题
}
```

## 10. 部署和运维

### 10.1 部署架构

#### 10.1.1 单机部署
```bash
# 启动 Chain Quote Server
./bin/grpc-client

# 启动 Model Processor
./model-processor
```

#### 10.1.2 集群部署
- **负载均衡**: 多实例负载均衡
- **服务发现**: 基于配置的服务发现
- **故障转移**: 自动故障检测和转移

### 10.2 监控指标

#### 10.2.1 系统指标
- **CPU 使用率**: 各服务的 CPU 使用情况
- **内存使用率**: 内存占用和 GC 情况
- **网络 I/O**: 网络流量和连接数
- **磁盘 I/O**: 数据库读写性能

#### 10.2.2 业务指标
- **交易处理量**: 每秒处理的交易数量
- **协议分布**: 各协议的交易占比
- **错误率**: 解析错误和处理错误率
- **延迟分布**: 端到端处理延迟分布

### 10.3 日志管理

#### 10.3.1 日志级别
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息和状态更新
- **WARN**: 警告信息和异常情况
- **ERROR**: 错误信息和异常堆栈

#### 10.3.2 日志格式
```go
log.SetFormatter(&logrus.TextFormatter{
    TimestampFormat: "2006-01-02 15:04:05",
    FullTimestamp:   true,
})
```

## 11. 安全考虑

### 11.1 网络安全
- **TLS 加密**: gRPC 连接使用 TLS 加密
- **访问控制**: 基于 Token 的访问控制
- **防火墙**: 网络层面的访问控制

### 11.2 数据安全
- **数据加密**: 敏感数据加密存储
- **访问审计**: 数据访问日志记录
- **备份恢复**: 定期数据备份和恢复测试

### 11.3 应用安全
- **输入验证**: 严格的输入数据验证
- **错误处理**: 安全的错误信息处理
- **资源限制**: 防止资源耗尽攻击

## 12. 故障处理

### 12.1 常见故障

#### 12.1.1 连接故障
- **gRPC 连接断开**: 自动重连机制
- **Kafka 连接失败**: 重试和降级策略
- **数据库连接超时**: 连接池管理和重试

#### 12.1.2 数据故障
- **消息解析失败**: 错误日志记录和跳过处理
- **数据库写入失败**: 重试机制和死信队列
- **数据不一致**: 数据校验和修复机制

### 12.2 恢复策略

#### 12.2.1 自动恢复
- **指数退避**: 连接重试使用指数退避
- **熔断机制**: 防止级联故障
- **健康检查**: 定期健康状态检查

#### 12.2.2 手动恢复
- **数据重放**: 支持历史数据重新处理
- **状态重置**: 支持服务状态重置
- **配置热更新**: 支持配置文件热更新

## 13. 性能调优

### 13.1 系统调优

#### 13.1.1 Go 运行时调优
```bash
export GOGC=100
export GOMAXPROCS=8
export GOMEMLIMIT=4GiB
```

#### 13.1.2 Kafka 调优
- **批量大小**: 调整批量处理大小
- **压缩算法**: 选择合适的压缩算法
- **分区数量**: 根据负载调整分区数量

### 13.2 数据库调优

#### 13.2.1 TDengine 调优
- **缓存配置**: 调整缓存大小
- **压缩配置**: 启用数据压缩
- **分片策略**: 优化数据分片

#### 13.2.2 查询优化
- **索引策略**: 创建合适的索引
- **查询重写**: 优化查询语句
- **缓存策略**: 实现查询结果缓存

## 14. 未来规划

### 14.1 功能增强
- **多链支持**: 扩展到 Ethereum、BSC 等其他链
- **高级分析**: 添加交易模式分析功能
- **机器学习**: 集成 ML 模型进行预测分析

### 14.2 性能提升
- **流处理**: 引入流处理框架提升性能
- **缓存层**: 添加 Redis 缓存层
- **CDN 集成**: 静态资源 CDN 加速

### 14.3 运维增强
- **自动化部署**: CI/CD 流水线自动化
- **监控告警**: 完善的监控告警体系
- **容器化**: Docker 和 Kubernetes 支持
